#!/usr/bin/env python
"""
NORYONAI Extended Forex Trading Agent

Enhanced forex agent with optional Qwen local integration for cost-effective,
private, and fast local analysis alongside cloud LLMs.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import sys
import os

# Import core modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.forex_agent import ForexAgent
from core.qwen_local import QwenIntegratedOrchestrator

class ExtendedForexAgent(ForexAgent):
    """Extended Forex Agent with Qwen local integration"""
    
    def __init__(self, config: Dict[str, Any], enable_qwen: bool = True):
        """Initialize extended forex agent with optional Qwen"""
        
        # Initialize base forex agent
        super().__init__(config)
        
        # Replace LLM orchestrator with extended version
        self.enable_qwen = enable_qwen
        if enable_qwen:
            try:
                self.llm_orchestrator = QwenIntegratedOrchestrator(config, enable_qwen=True)
                print("✅ Extended Forex Agent with Qwen integration initialized")
            except Exception as e:
                print(f"⚠️ Qwen integration failed, falling back to standard LLMs: {e}")
                self.enable_qwen = False
        
        if not self.enable_qwen:
            print("📊 Extended Forex Agent initialized (Qwen disabled)")
    
    async def analyze_forex_pair_extended(self, 
                                        pair: str, 
                                        timeframe: str = '1h',
                                        lookback_periods: int = 100,
                                        use_qwen: bool = None) -> Dict[str, Any]:
        """Enhanced forex analysis with optional Qwen integration"""
        
        # Use instance setting if not specified
        if use_qwen is None:
            use_qwen = self.enable_qwen
        
        print(f"💱 Extended analysis for {pair} (Qwen: {'✅' if use_qwen else '❌'})")
        
        try:
            # 1. Get market data
            market_data = await self._get_forex_data(pair, timeframe, lookback_periods)
            
            # 2. Calculate technical indicators
            df = self._prepare_dataframe(market_data)
            indicators = self.technical_indicators.calculate_all_indicators(df)
            
            # 3. Add forex-specific indicators
            forex_indicators = self._calculate_forex_specific_indicators(df, pair)
            indicators.update(forex_indicators)
            
            # 4. Get market context
            market_context = self._get_forex_market_context(pair)
            
            # 5. Get LLM consensus (with or without Qwen)
            if use_qwen and hasattr(self.llm_orchestrator, 'get_trading_consensus_with_qwen'):
                llm_analysis = await self.llm_orchestrator.get_trading_consensus_with_qwen(
                    pair, market_data, indicators, "forex"
                )
            else:
                llm_analysis = await self.llm_orchestrator.get_trading_consensus(
                    pair, market_data, indicators, "forex"
                )
            
            # 6. Generate enhanced recommendation
            recommendation = self._generate_enhanced_forex_recommendation(
                pair, indicators, llm_analysis, market_context, use_qwen
            )
            
            return {
                'pair': pair,
                'timeframe': timeframe,
                'market_data': market_data,
                'technical_indicators': indicators,
                'market_context': market_context,
                'llm_analysis': llm_analysis,
                'recommendation': recommendation,
                'qwen_enabled': use_qwen,
                'analysis_type': 'extended_with_qwen' if use_qwen else 'standard',
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Extended analysis error for {pair}: {e}")
            return {'error': str(e), 'pair': pair}
    
    def _generate_enhanced_forex_recommendation(self, 
                                              pair: str,
                                              indicators: Dict[str, float],
                                              llm_analysis: Dict[str, Any],
                                              market_context: Dict[str, Any],
                                              qwen_enabled: bool) -> Dict[str, Any]:
        """Generate enhanced recommendation with Qwen insights"""
        
        # Get base recommendation
        base_recommendation = self._generate_forex_recommendation(
            pair, indicators, llm_analysis, market_context
        )
        
        # Add Qwen-specific insights if available
        qwen_insights = {}
        if qwen_enabled and 'qwen_local' in llm_analysis:
            qwen_data = llm_analysis['qwen_local']
            if 'analysis' in qwen_data:
                qwen_analysis = qwen_data['analysis']
                qwen_insights = {
                    'qwen_action': qwen_analysis.get('action', 'UNKNOWN'),
                    'qwen_confidence': qwen_analysis.get('confidence', 0),
                    'qwen_reasoning': qwen_analysis.get('reasoning', ''),
                    'qwen_risk_level': qwen_analysis.get('risk_level', 'UNKNOWN'),
                    'qwen_key_factors': qwen_analysis.get('key_factors', [])
                }
        
        # Enhanced scoring with Qwen
        enhanced_scores = self._calculate_enhanced_scores(
            indicators, llm_analysis, market_context, qwen_insights
        )
        
        # Update recommendation with enhanced data
        base_recommendation.update({
            'enhanced_scores': enhanced_scores,
            'qwen_insights': qwen_insights,
            'analysis_method': 'extended_multi_llm_with_qwen' if qwen_enabled else 'standard_multi_llm',
            'total_llms_consulted': len(llm_analysis.get('individual_analyses', [])),
            'local_analysis_included': qwen_enabled and bool(qwen_insights)
        })
        
        return base_recommendation
    
    def _calculate_enhanced_scores(self, 
                                 indicators: Dict[str, float],
                                 llm_analysis: Dict[str, Any],
                                 market_context: Dict[str, Any],
                                 qwen_insights: Dict[str, Any]) -> Dict[str, float]:
        """Calculate enhanced scoring with Qwen insights"""
        
        # Base scores
        tech_score = self._calculate_technical_score(indicators)
        context_score = self._calculate_context_score(market_context, "")
        
        # LLM consensus score
        consensus = llm_analysis.get('consensus', {})
        llm_score = consensus.get('confidence', 0.5)
        
        # Qwen local score (if available)
        qwen_score = qwen_insights.get('qwen_confidence', 0.5) if qwen_insights else 0.5
        
        # Enhanced weighting
        if qwen_insights:
            # With Qwen: Give more weight to local analysis for privacy/speed
            combined_score = (
                llm_score * 0.35 +      # Cloud LLMs
                qwen_score * 0.25 +     # Local Qwen
                tech_score * 0.25 +     # Technical analysis
                context_score * 0.15    # Market context
            )
        else:
            # Without Qwen: Standard weighting
            combined_score = (
                llm_score * 0.4 +       # Cloud LLMs
                tech_score * 0.4 +      # Technical analysis
                context_score * 0.2     # Market context
            )
        
        return {
            'technical_score': tech_score,
            'context_score': context_score,
            'llm_consensus_score': llm_score,
            'qwen_local_score': qwen_score,
            'combined_score': combined_score,
            'scoring_method': 'enhanced_with_qwen' if qwen_insights else 'standard'
        }
    
    async def compare_analysis_methods(self, pair: str) -> Dict[str, Any]:
        """Compare standard vs Qwen-enhanced analysis"""
        
        print(f"🔬 Comparing analysis methods for {pair}")
        
        # Run both analyses
        standard_analysis = await self.analyze_forex_pair_extended(pair, use_qwen=False)
        qwen_analysis = await self.analyze_forex_pair_extended(pair, use_qwen=True)
        
        # Compare results
        comparison = {
            'pair': pair,
            'timestamp': datetime.now().isoformat(),
            'standard_analysis': {
                'action': standard_analysis.get('recommendation', {}).get('action', 'UNKNOWN'),
                'confidence': standard_analysis.get('recommendation', {}).get('confidence', 0),
                'llms_used': len(standard_analysis.get('llm_analysis', {}).get('individual_analyses', [])),
                'processing_time': 'API_dependent'
            },
            'qwen_enhanced_analysis': {
                'action': qwen_analysis.get('recommendation', {}).get('action', 'UNKNOWN'),
                'confidence': qwen_analysis.get('recommendation', {}).get('confidence', 0),
                'llms_used': len(qwen_analysis.get('llm_analysis', {}).get('individual_analyses', [])),
                'local_analysis': qwen_analysis.get('recommendation', {}).get('local_analysis_included', False),
                'processing_time': 'faster_local'
            },
            'agreement': self._calculate_agreement(standard_analysis, qwen_analysis),
            'recommendation': self._get_comparison_recommendation(standard_analysis, qwen_analysis)
        }
        
        return comparison
    
    def _calculate_agreement(self, standard: Dict, qwen: Dict) -> Dict[str, Any]:
        """Calculate agreement between analysis methods"""
        
        std_action = standard.get('recommendation', {}).get('action', 'UNKNOWN')
        qwen_action = qwen.get('recommendation', {}).get('action', 'UNKNOWN')
        
        std_conf = standard.get('recommendation', {}).get('confidence', 0)
        qwen_conf = qwen.get('recommendation', {}).get('confidence', 0)
        
        action_agreement = std_action == qwen_action
        confidence_diff = abs(std_conf - qwen_conf)
        
        return {
            'action_agreement': action_agreement,
            'confidence_difference': confidence_diff,
            'agreement_level': 'HIGH' if action_agreement and confidence_diff < 0.1 else 
                             'MEDIUM' if action_agreement else 'LOW'
        }
    
    def _get_comparison_recommendation(self, standard: Dict, qwen: Dict) -> Dict[str, Any]:
        """Get final recommendation from comparison"""
        
        agreement = self._calculate_agreement(standard, qwen)
        
        if agreement['agreement_level'] == 'HIGH':
            # High agreement: Use Qwen-enhanced for better insights
            return {
                'recommended_method': 'qwen_enhanced',
                'reasoning': 'High agreement between methods, Qwen provides additional local insights',
                'final_action': qwen.get('recommendation', {}).get('action', 'HOLD'),
                'confidence_boost': 0.1  # Boost confidence when methods agree
            }
        elif agreement['agreement_level'] == 'MEDIUM':
            # Medium agreement: Average the results
            std_conf = standard.get('recommendation', {}).get('confidence', 0)
            qwen_conf = qwen.get('recommendation', {}).get('confidence', 0)
            avg_conf = (std_conf + qwen_conf) / 2
            
            return {
                'recommended_method': 'averaged',
                'reasoning': 'Methods agree on action but differ in confidence',
                'final_action': standard.get('recommendation', {}).get('action', 'HOLD'),
                'averaged_confidence': avg_conf
            }
        else:
            # Low agreement: Use more conservative approach
            return {
                'recommended_method': 'conservative',
                'reasoning': 'Methods disagree, recommend HOLD for safety',
                'final_action': 'HOLD',
                'conflict_detected': True
            }
    
    def get_qwen_status(self) -> Dict[str, Any]:
        """Get Qwen integration status"""
        
        status = {
            'qwen_enabled': self.enable_qwen,
            'qwen_available': False,
            'model_info': {}
        }
        
        if self.enable_qwen and hasattr(self.llm_orchestrator, 'qwen_analyzer'):
            qwen_analyzer = self.llm_orchestrator.qwen_analyzer
            if qwen_analyzer:
                status['qwen_available'] = True
                status['model_info'] = qwen_analyzer.get_model_info()
        
        return status

# Test function
async def test_extended_forex_agent():
    """Test the extended forex agent"""
    
    print("🧪 Testing Extended Forex Agent...")
    
    # Mock config
    config = {
        'openai_api_key': 'test-key',
        'claude_api_key': 'test-key',
        'gemini_api_key': 'test-key',
        'deepseek_api_key': 'test-key'
    }
    
    # Test with Qwen enabled
    agent = ExtendedForexAgent(config, enable_qwen=True)
    
    # Test Qwen status
    status = agent.get_qwen_status()
    print(f"📊 Qwen Status: {status}")
    
    # Test extended analysis
    result = await agent.analyze_forex_pair_extended('EURUSD')
    
    print("💱 EXTENDED FOREX ANALYSIS:")
    print(f"Action: {result.get('recommendation', {}).get('action', 'N/A')}")
    print(f"Confidence: {result.get('recommendation', {}).get('confidence', 0):.1%}")
    print(f"Qwen Enabled: {result.get('qwen_enabled', False)}")
    print(f"LLMs Consulted: {result.get('recommendation', {}).get('total_llms_consulted', 0)}")
    
    # Test comparison
    if status['qwen_available']:
        comparison = await agent.compare_analysis_methods('EURUSD')
        print(f"\n🔬 ANALYSIS COMPARISON:")
        print(f"Agreement Level: {comparison['agreement']['agreement_level']}")
        print(f"Recommended Method: {comparison['recommendation']['recommended_method']}")

if __name__ == "__main__":
    asyncio.run(test_extended_forex_agent()) 