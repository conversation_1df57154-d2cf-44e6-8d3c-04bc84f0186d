#!/usr/bin/env python
"""
NORYONAI Configuration Settings

Centralized configuration for API keys, trading parameters, and system settings.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
import json
from pathlib import Path

@dataclass
class LLMConfig:
    """LLM API configuration"""
    openai_api_key: Optional[str] = None
    claude_api_key: Optional[str] = None
    gemini_api_key: Optional[str] = None
    deepseek_api_key: Optional[str] = None
    
    # Model preferences
    openai_model: str = "gpt-4-turbo-preview"
    claude_model: str = "claude-3-sonnet-20240229"
    gemini_model: str = "gemini-pro"
    deepseek_model: str = "deepseek-chat"

@dataclass
class TradingConfig:
    """Trading configuration"""
    # Risk management
    max_position_size: float = 0.02  # 2% max per trade
    max_daily_risk: float = 0.05     # 5% max daily risk
    max_drawdown: float = 0.10       # 10% max drawdown
    
    # Position sizing
    base_position_size: float = 0.01  # 1% base position
    confidence_multiplier: float = 2.0  # Max 2x based on confidence
    
    # Stop loss and take profit
    default_stop_loss_pips: int = 20
    default_take_profit_ratio: float = 2.0  # 1:2 risk/reward
    
    # Trading hours (UTC)
    trading_start_hour: int = 0
    trading_end_hour: int = 23
    
    # Markets to trade
    trade_forex: bool = True
    trade_crypto: bool = True
    trade_equity: bool = True

@dataclass
class DataConfig:
    """Data feed configuration"""
    # Data providers
    forex_provider: str = "oanda"  # oanda, fxcm, alpha_vantage
    crypto_provider: str = "binance"  # binance, coinbase, kraken
    equity_provider: str = "alpha_vantage"  # alpha_vantage, yahoo, iex
    
    # API keys for data providers
    alpha_vantage_key: Optional[str] = None
    oanda_api_key: Optional[str] = None
    oanda_account_id: Optional[str] = None
    binance_api_key: Optional[str] = None
    binance_secret_key: Optional[str] = None
    
    # Data settings
    default_timeframe: str = "1h"
    lookback_periods: int = 100
    update_frequency: int = 60  # seconds

@dataclass
class SystemConfig:
    """System configuration"""
    # Logging
    log_level: str = "INFO"
    log_file: str = "logs/noryonai.log"
    
    # Performance
    max_concurrent_analyses: int = 5
    cache_duration: int = 300  # 5 minutes
    
    # Alerts
    enable_alerts: bool = True
    alert_webhook: Optional[str] = None
    
    # Backtesting
    backtest_start_date: str = "2023-01-01"
    backtest_end_date: str = "2024-01-01"

class ConfigManager:
    """Manages all configuration settings"""
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize configuration manager"""
        self.config_file = config_file or "config/config.json"
        self.config_dir = Path("config")
        self.config_dir.mkdir(exist_ok=True)
        
        # Initialize configurations
        self.llm_config = LLMConfig()
        self.trading_config = TradingConfig()
        self.data_config = DataConfig()
        self.system_config = SystemConfig()
        
        # Load configuration
        self.load_config()
        
        print("⚙️ Configuration Manager initialized")
    
    def load_config(self):
        """Load configuration from file and environment variables"""
        
        # Load from file if exists
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)
                self._update_from_dict(config_data)
                print(f"✅ Loaded configuration from {self.config_file}")
            except Exception as e:
                print(f"⚠️ Error loading config file: {e}")
        
        # Override with environment variables
        self._load_from_env()
        
        # Validate configuration
        self._validate_config()
    
    def _update_from_dict(self, config_data: Dict[str, Any]):
        """Update configuration from dictionary"""
        
        # Update LLM config
        if 'llm' in config_data:
            llm_data = config_data['llm']
            for key, value in llm_data.items():
                if hasattr(self.llm_config, key):
                    setattr(self.llm_config, key, value)
        
        # Update trading config
        if 'trading' in config_data:
            trading_data = config_data['trading']
            for key, value in trading_data.items():
                if hasattr(self.trading_config, key):
                    setattr(self.trading_config, key, value)
        
        # Update data config
        if 'data' in config_data:
            data_data = config_data['data']
            for key, value in data_data.items():
                if hasattr(self.data_config, key):
                    setattr(self.data_config, key, value)
        
        # Update system config
        if 'system' in config_data:
            system_data = config_data['system']
            for key, value in system_data.items():
                if hasattr(self.system_config, key):
                    setattr(self.system_config, key, value)
    
    def _load_from_env(self):
        """Load configuration from environment variables"""
        
        # LLM API keys
        self.llm_config.openai_api_key = os.getenv('OPENAI_API_KEY') or self.llm_config.openai_api_key
        self.llm_config.claude_api_key = os.getenv('CLAUDE_API_KEY') or self.llm_config.claude_api_key
        self.llm_config.gemini_api_key = os.getenv('GEMINI_API_KEY') or self.llm_config.gemini_api_key
        self.llm_config.deepseek_api_key = os.getenv('DEEPSEEK_API_KEY') or self.llm_config.deepseek_api_key
        
        # Data provider keys
        self.data_config.alpha_vantage_key = os.getenv('ALPHA_VANTAGE_KEY') or self.data_config.alpha_vantage_key
        self.data_config.oanda_api_key = os.getenv('OANDA_API_KEY') or self.data_config.oanda_api_key
        self.data_config.oanda_account_id = os.getenv('OANDA_ACCOUNT_ID') or self.data_config.oanda_account_id
        self.data_config.binance_api_key = os.getenv('BINANCE_API_KEY') or self.data_config.binance_api_key
        self.data_config.binance_secret_key = os.getenv('BINANCE_SECRET_KEY') or self.data_config.binance_secret_key
        
        # System settings
        if os.getenv('LOG_LEVEL'):
            self.system_config.log_level = os.getenv('LOG_LEVEL')
        
        if os.getenv('ALERT_WEBHOOK'):
            self.system_config.alert_webhook = os.getenv('ALERT_WEBHOOK')
    
    def _validate_config(self):
        """Validate configuration settings"""
        
        warnings = []
        
        # Check LLM API keys
        if not any([
            self.llm_config.openai_api_key,
            self.llm_config.claude_api_key,
            self.llm_config.gemini_api_key,
            self.llm_config.deepseek_api_key
        ]):
            warnings.append("No LLM API keys configured - system will run in demo mode")
        
        # Check trading parameters
        if self.trading_config.max_position_size > 0.05:
            warnings.append("Max position size > 5% - high risk configuration")
        
        if self.trading_config.max_daily_risk > 0.10:
            warnings.append("Max daily risk > 10% - very high risk configuration")
        
        # Check data providers
        if not any([
            self.data_config.alpha_vantage_key,
            self.data_config.oanda_api_key,
            self.data_config.binance_api_key
        ]):
            warnings.append("No data provider API keys - using mock data")
        
        # Print warnings
        for warning in warnings:
            print(f"⚠️ {warning}")
        
        if not warnings:
            print("✅ Configuration validation passed")
    
    def save_config(self):
        """Save current configuration to file"""
        
        config_data = {
            'llm': {
                'openai_model': self.llm_config.openai_model,
                'claude_model': self.llm_config.claude_model,
                'gemini_model': self.llm_config.gemini_model,
                'deepseek_model': self.llm_config.deepseek_model
            },
            'trading': {
                'max_position_size': self.trading_config.max_position_size,
                'max_daily_risk': self.trading_config.max_daily_risk,
                'max_drawdown': self.trading_config.max_drawdown,
                'base_position_size': self.trading_config.base_position_size,
                'confidence_multiplier': self.trading_config.confidence_multiplier,
                'default_stop_loss_pips': self.trading_config.default_stop_loss_pips,
                'default_take_profit_ratio': self.trading_config.default_take_profit_ratio,
                'trading_start_hour': self.trading_config.trading_start_hour,
                'trading_end_hour': self.trading_config.trading_end_hour,
                'trade_forex': self.trading_config.trade_forex,
                'trade_crypto': self.trading_config.trade_crypto,
                'trade_equity': self.trading_config.trade_equity
            },
            'data': {
                'forex_provider': self.data_config.forex_provider,
                'crypto_provider': self.data_config.crypto_provider,
                'equity_provider': self.data_config.equity_provider,
                'default_timeframe': self.data_config.default_timeframe,
                'lookback_periods': self.data_config.lookback_periods,
                'update_frequency': self.data_config.update_frequency
            },
            'system': {
                'log_level': self.system_config.log_level,
                'log_file': self.system_config.log_file,
                'max_concurrent_analyses': self.system_config.max_concurrent_analyses,
                'cache_duration': self.system_config.cache_duration,
                'enable_alerts': self.system_config.enable_alerts,
                'backtest_start_date': self.system_config.backtest_start_date,
                'backtest_end_date': self.system_config.backtest_end_date
            }
        }
        
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            print(f"✅ Configuration saved to {self.config_file}")
        except Exception as e:
            print(f"❌ Error saving configuration: {e}")
    
    def get_llm_config_dict(self) -> Dict[str, str]:
        """Get LLM configuration as dictionary for LLMOrchestrator"""
        
        return {
            'openai_api_key': self.llm_config.openai_api_key,
            'claude_api_key': self.llm_config.claude_api_key,
            'gemini_api_key': self.llm_config.gemini_api_key,
            'deepseek_api_key': self.llm_config.deepseek_api_key
        }
    
    def create_sample_config(self):
        """Create a sample configuration file"""
        
        sample_config = {
            "llm": {
                "openai_api_key": "your-openai-api-key-here",
                "claude_api_key": "your-claude-api-key-here",
                "gemini_api_key": "your-gemini-api-key-here",
                "deepseek_api_key": "your-deepseek-api-key-here",
                "openai_model": "gpt-4-turbo-preview",
                "claude_model": "claude-3-sonnet-20240229"
            },
            "trading": {
                "max_position_size": 0.02,
                "max_daily_risk": 0.05,
                "base_position_size": 0.01,
                "default_stop_loss_pips": 20,
                "trade_forex": True,
                "trade_crypto": True,
                "trade_equity": True
            },
            "data": {
                "forex_provider": "oanda",
                "crypto_provider": "binance",
                "alpha_vantage_key": "your-alpha-vantage-key",
                "oanda_api_key": "your-oanda-api-key",
                "binance_api_key": "your-binance-api-key"
            },
            "system": {
                "log_level": "INFO",
                "enable_alerts": True,
                "max_concurrent_analyses": 5
            }
        }
        
        sample_file = "config/config_sample.json"
        try:
            with open(sample_file, 'w') as f:
                json.dump(sample_config, f, indent=2)
            print(f"✅ Sample configuration created: {sample_file}")
            print("📝 Copy this to config.json and add your API keys")
        except Exception as e:
            print(f"❌ Error creating sample config: {e}")
    
    def print_status(self):
        """Print configuration status"""
        
        print("\n⚙️ NORYONAI CONFIGURATION STATUS")
        print("=" * 50)
        
        # LLM APIs
        print("\n🤖 LLM APIs:")
        print(f"  OpenAI: {'✅' if self.llm_config.openai_api_key else '❌'}")
        print(f"  Claude: {'✅' if self.llm_config.claude_api_key else '❌'}")
        print(f"  Gemini: {'✅' if self.llm_config.gemini_api_key else '❌'}")
        print(f"  DeepSeek: {'✅' if self.llm_config.deepseek_api_key else '❌'}")
        
        # Data Providers
        print("\n📊 Data Providers:")
        print(f"  Alpha Vantage: {'✅' if self.data_config.alpha_vantage_key else '❌'}")
        print(f"  OANDA: {'✅' if self.data_config.oanda_api_key else '❌'}")
        print(f"  Binance: {'✅' if self.data_config.binance_api_key else '❌'}")
        
        # Trading Settings
        print("\n💰 Trading Settings:")
        print(f"  Max Position Size: {self.trading_config.max_position_size:.1%}")
        print(f"  Max Daily Risk: {self.trading_config.max_daily_risk:.1%}")
        print(f"  Markets: Forex={self.trading_config.trade_forex}, "
              f"Crypto={self.trading_config.trade_crypto}, "
              f"Equity={self.trading_config.trade_equity}")
        
        print()

# Global configuration instance
config_manager = ConfigManager()

# Convenience functions
def get_config() -> ConfigManager:
    """Get the global configuration manager"""
    return config_manager

def get_llm_config() -> Dict[str, str]:
    """Get LLM configuration for API initialization"""
    return config_manager.get_llm_config_dict()

def get_trading_config() -> TradingConfig:
    """Get trading configuration"""
    return config_manager.trading_config

def get_data_config() -> DataConfig:
    """Get data configuration"""
    return config_manager.data_config

def get_system_config() -> SystemConfig:
    """Get system configuration"""
    return config_manager.system_config

# Test function
def test_config():
    """Test the configuration system"""
    
    print("🧪 Testing Configuration System...")
    
    # Create sample config
    config_manager.create_sample_config()
    
    # Print status
    config_manager.print_status()
    
    # Test saving
    config_manager.save_config()
    
    print("✅ Configuration system test complete")

if __name__ == "__main__":
    test_config() 