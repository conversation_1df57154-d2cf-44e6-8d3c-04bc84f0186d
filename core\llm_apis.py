#!/usr/bin/env python
"""
NORYONAI LLM APIs - Real Multi-LLM Trading Intelligence

Integrates Claude, OpenAI, Gemini, and DeepSeek for trading decisions.
Each LLM has specific strengths we leverage.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime

# API clients
import openai
import anthropic
import google.generativeai as genai
from openai import OpenAI

@dataclass
class MarketAnalysis:
    """Structured market analysis from LLMs"""
    llm_source: str
    symbol: str
    action: str  # BUY, SELL, HOLD
    confidence: float  # 0-1
    reasoning: str
    risk_level: str  # LOW, MEDIUM, HIGH
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    timestamp: datetime = None

class LLMOrchestrator:
    """Orchestrates multiple LLMs for trading decisions"""
    
    def __init__(self, config: Dict[str, str]):
        """Initialize all LLM clients"""
        self.config = config
        
        # Initialize clients
        self.openai_client = OpenAI(api_key=config.get('openai_api_key'))
        self.anthropic_client = anthropic.Anthropic(api_key=config.get('claude_api_key'))
        
        # Gemini setup
        if config.get('gemini_api_key'):
            genai.configure(api_key=config['gemini_api_key'])
            self.gemini_model = genai.GenerativeModel('gemini-pro')
        
        # DeepSeek setup (using OpenAI-compatible API)
        self.deepseek_client = OpenAI(
            api_key=config.get('deepseek_api_key'),
            base_url="https://api.deepseek.com/v1"
        ) if config.get('deepseek_api_key') else None
        
        print("🤖 LLM Orchestrator initialized with multiple AI models")
    
    async def get_trading_consensus(self, 
                                  symbol: str, 
                                  market_data: Dict[str, Any],
                                  indicators: Dict[str, Any],
                                  market_type: str = "forex") -> Dict[str, Any]:
        """Get trading consensus from all LLMs"""
        
        print(f"🧠 Getting multi-LLM analysis for {symbol}")
        
        # Prepare market context
        context = self._prepare_market_context(symbol, market_data, indicators, market_type)
        
        # Get analysis from each LLM
        tasks = [
            self._get_claude_analysis(context),
            self._get_openai_analysis(context),
            self._get_gemini_analysis(context),
            self._get_deepseek_analysis(context)
        ]
        
        # Run all LLMs concurrently
        analyses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful analyses
        valid_analyses = [a for a in analyses if isinstance(a, MarketAnalysis)]
        
        if not valid_analyses:
            return {"error": "No LLM analyses available"}
        
        # Create consensus
        consensus = self._create_consensus(valid_analyses)
        
        return {
            "symbol": symbol,
            "consensus": consensus,
            "individual_analyses": [self._analysis_to_dict(a) for a in valid_analyses],
            "timestamp": datetime.now().isoformat()
        }
    
    def _prepare_market_context(self, symbol: str, market_data: Dict, indicators: Dict, market_type: str) -> str:
        """Prepare comprehensive market context for LLMs"""
        
        context = f"""
TRADING ANALYSIS REQUEST - {market_type.upper()} MARKET

Symbol: {symbol}
Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}

CURRENT MARKET DATA:
- Price: {market_data.get('price', 'N/A')}
- Volume: {market_data.get('volume', 'N/A')}
- 24h Change: {market_data.get('change_24h', 'N/A')}%
- High: {market_data.get('high', 'N/A')}
- Low: {market_data.get('low', 'N/A')}

TECHNICAL INDICATORS:
"""
        
        # Add indicators
        for indicator, value in indicators.items():
            context += f"- {indicator}: {value}\n"
        
        context += f"""

ANALYSIS REQUIREMENTS:
1. Provide a clear trading action: BUY, SELL, or HOLD
2. Give confidence level (0-100%)
3. Explain your reasoning based on the data
4. Assess risk level: LOW, MEDIUM, or HIGH
5. Suggest target price and stop loss if recommending BUY/SELL

Focus on {market_type} market characteristics and respond with actionable trading advice.
"""
        
        return context
    
    async def _get_claude_analysis(self, context: str) -> MarketAnalysis:
        """Get analysis from Claude (best for reasoning)"""
        try:
            response = await asyncio.to_thread(
                self.anthropic_client.messages.create,
                model="claude-3-sonnet-20240229",
                max_tokens=1000,
                messages=[{
                    "role": "user", 
                    "content": f"{context}\n\nProvide detailed technical analysis with clear reasoning."
                }]
            )
            
            return self._parse_llm_response(response.content[0].text, "Claude")
            
        except Exception as e:
            print(f"❌ Claude analysis failed: {e}")
            raise e
    
    async def _get_openai_analysis(self, context: str) -> MarketAnalysis:
        """Get analysis from OpenAI GPT-4 (best for pattern recognition)"""
        try:
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model="gpt-4-turbo-preview",
                messages=[{
                    "role": "system",
                    "content": "You are an expert quantitative trader. Analyze market data and provide precise trading recommendations."
                }, {
                    "role": "user",
                    "content": f"{context}\n\nFocus on pattern recognition and market momentum."
                }],
                max_tokens=1000
            )
            
            return self._parse_llm_response(response.choices[0].message.content, "OpenAI")
            
        except Exception as e:
            print(f"❌ OpenAI analysis failed: {e}")
            raise e
    
    async def _get_gemini_analysis(self, context: str) -> MarketAnalysis:
        """Get analysis from Gemini (best for multi-modal analysis)"""
        try:
            if not hasattr(self, 'gemini_model'):
                raise Exception("Gemini not configured")
            
            prompt = f"{context}\n\nProvide comprehensive market analysis with risk assessment."
            
            response = await asyncio.to_thread(
                self.gemini_model.generate_content,
                prompt
            )
            
            return self._parse_llm_response(response.text, "Gemini")
            
        except Exception as e:
            print(f"❌ Gemini analysis failed: {e}")
            raise e
    
    async def _get_deepseek_analysis(self, context: str) -> MarketAnalysis:
        """Get analysis from DeepSeek (best for mathematical reasoning)"""
        try:
            if not self.deepseek_client:
                raise Exception("DeepSeek not configured")
            
            response = await asyncio.to_thread(
                self.deepseek_client.chat.completions.create,
                model="deepseek-chat",
                messages=[{
                    "role": "system",
                    "content": "You are a quantitative analyst specializing in mathematical trading models. Provide data-driven analysis."
                }, {
                    "role": "user",
                    "content": f"{context}\n\nFocus on mathematical indicators and statistical analysis."
                }],
                max_tokens=1000
            )
            
            return self._parse_llm_response(response.choices[0].message.content, "DeepSeek")
            
        except Exception as e:
            print(f"❌ DeepSeek analysis failed: {e}")
            raise e
    
    def _parse_llm_response(self, response_text: str, llm_source: str) -> MarketAnalysis:
        """Parse LLM response into structured analysis"""
        
        # Extract key information using simple parsing
        # In production, you might want more sophisticated parsing
        
        text = response_text.lower()
        
        # Determine action
        if "buy" in text and "sell" not in text:
            action = "BUY"
        elif "sell" in text and "buy" not in text:
            action = "SELL"
        else:
            action = "HOLD"
        
        # Extract confidence (look for percentages)
        confidence = 0.5  # default
        import re
        conf_match = re.search(r'(\d+)%', response_text)
        if conf_match:
            confidence = float(conf_match.group(1)) / 100
        
        # Determine risk level
        if "high risk" in text or "risky" in text:
            risk_level = "HIGH"
        elif "low risk" in text or "safe" in text:
            risk_level = "LOW"
        else:
            risk_level = "MEDIUM"
        
        return MarketAnalysis(
            llm_source=llm_source,
            symbol="",  # Will be set by caller
            action=action,
            confidence=confidence,
            reasoning=response_text[:500],  # Truncate for storage
            risk_level=risk_level,
            timestamp=datetime.now()
        )
    
    def _create_consensus(self, analyses: List[MarketAnalysis]) -> Dict[str, Any]:
        """Create consensus from multiple LLM analyses"""
        
        if not analyses:
            return {"action": "HOLD", "confidence": 0, "reasoning": "No analyses available"}
        
        # Count votes
        actions = [a.action for a in analyses]
        action_counts = {action: actions.count(action) for action in set(actions)}
        
        # Get majority action
        consensus_action = max(action_counts, key=action_counts.get)
        
        # Average confidence for consensus action
        consensus_analyses = [a for a in analyses if a.action == consensus_action]
        avg_confidence = sum(a.confidence for a in consensus_analyses) / len(consensus_analyses)
        
        # Combine reasoning
        reasoning_summary = f"Consensus from {len(analyses)} LLMs: "
        reasoning_summary += f"{action_counts[consensus_action]}/{len(analyses)} recommend {consensus_action}. "
        
        return {
            "action": consensus_action,
            "confidence": round(avg_confidence, 2),
            "reasoning": reasoning_summary,
            "vote_breakdown": action_counts,
            "participating_llms": [a.llm_source for a in analyses]
        }
    
    def _analysis_to_dict(self, analysis: MarketAnalysis) -> Dict[str, Any]:
        """Convert analysis to dictionary"""
        return {
            "llm_source": analysis.llm_source,
            "action": analysis.action,
            "confidence": analysis.confidence,
            "reasoning": analysis.reasoning,
            "risk_level": analysis.risk_level,
            "timestamp": analysis.timestamp.isoformat() if analysis.timestamp else None
        }

# Quick test function
async def test_llm_apis():
    """Test the LLM API integration"""
    
    # Mock config (you'll need real API keys)
    config = {
        'openai_api_key': 'your-openai-key',
        'claude_api_key': 'your-claude-key',
        'gemini_api_key': 'your-gemini-key',
        'deepseek_api_key': 'your-deepseek-key'
    }
    
    orchestrator = LLMOrchestrator(config)
    
    # Mock market data
    market_data = {
        'price': 1.0850,
        'volume': 1500000,
        'change_24h': 0.25,
        'high': 1.0875,
        'low': 1.0820
    }
    
    indicators = {
        'RSI': 65.5,
        'MACD': 0.0012,
        'BB_Position': 0.75,
        'Volume_Ratio': 1.2
    }
    
    result = await orchestrator.get_trading_consensus(
        "EURUSD", market_data, indicators, "forex"
    )
    
    print("🎯 Multi-LLM Trading Consensus:")
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    asyncio.run(test_llm_apis()) 