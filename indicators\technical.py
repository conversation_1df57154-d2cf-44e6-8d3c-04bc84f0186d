#!/usr/bin/env python
"""
NORYONAI Technical Indicators - Comprehensive Real Algorithms

Massive collection of technical indicators and algorithms to feed the AI models.
All calculations are real and production-ready.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import talib
from scipy import stats
from scipy.signal import find_peaks

@dataclass
class IndicatorResult:
    """Structured indicator result"""
    name: str
    value: float
    signal: str  # BUY, SELL, NEUTRAL
    strength: float  # 0-1
    description: str

class TechnicalIndicators:
    """Comprehensive technical indicators calculator"""
    
    def __init__(self):
        self.indicators = {}
        print("📊 Technical Indicators engine initialized")
    
    def calculate_all_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate ALL technical indicators for the AI models"""
        
        print("🔢 Calculating comprehensive technical indicators...")
        
        # Ensure we have required columns
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in required_cols:
            if col not in df.columns:
                raise ValueError(f"Missing required column: {col}")
        
        indicators = {}
        
        # 1. TREND INDICATORS
        indicators.update(self._calculate_trend_indicators(df))
        
        # 2. MOMENTUM INDICATORS  
        indicators.update(self._calculate_momentum_indicators(df))
        
        # 3. VOLATILITY INDICATORS
        indicators.update(self._calculate_volatility_indicators(df))
        
        # 4. VOLUME INDICATORS
        indicators.update(self._calculate_volume_indicators(df))
        
        # 5. SUPPORT/RESISTANCE INDICATORS
        indicators.update(self._calculate_support_resistance(df))
        
        # 6. PATTERN RECOGNITION
        indicators.update(self._calculate_patterns(df))
        
        # 7. STATISTICAL INDICATORS
        indicators.update(self._calculate_statistical_indicators(df))
        
        # 8. CUSTOM ALGORITHMS
        indicators.update(self._calculate_custom_algorithms(df))
        
        print(f"✅ Calculated {len(indicators)} technical indicators")
        return indicators
    
    def _calculate_trend_indicators(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate trend-following indicators"""
        
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        
        indicators = {}
        
        # Moving Averages
        for period in [5, 10, 20, 50, 100, 200]:
            sma = talib.SMA(close, timeperiod=period)
            ema = talib.EMA(close, timeperiod=period)
            
            indicators[f'SMA_{period}'] = sma[-1] if not np.isnan(sma[-1]) else 0
            indicators[f'EMA_{period}'] = ema[-1] if not np.isnan(ema[-1]) else 0
            
            # Price position relative to MA
            indicators[f'Price_vs_SMA_{period}'] = (close[-1] - sma[-1]) / sma[-1] if not np.isnan(sma[-1]) else 0
            indicators[f'Price_vs_EMA_{period}'] = (close[-1] - ema[-1]) / ema[-1] if not np.isnan(ema[-1]) else 0
        
        # MACD
        macd, macd_signal, macd_hist = talib.MACD(close)
        indicators['MACD'] = macd[-1] if not np.isnan(macd[-1]) else 0
        indicators['MACD_Signal'] = macd_signal[-1] if not np.isnan(macd_signal[-1]) else 0
        indicators['MACD_Histogram'] = macd_hist[-1] if not np.isnan(macd_hist[-1]) else 0
        
        # Parabolic SAR
        sar = talib.SAR(high, low)
        indicators['SAR'] = sar[-1] if not np.isnan(sar[-1]) else 0
        indicators['SAR_Signal'] = 1 if close[-1] > sar[-1] else -1
        
        # ADX (Average Directional Index)
        adx = talib.ADX(high, low, close)
        plus_di = talib.PLUS_DI(high, low, close)
        minus_di = talib.MINUS_DI(high, low, close)
        
        indicators['ADX'] = adx[-1] if not np.isnan(adx[-1]) else 0
        indicators['Plus_DI'] = plus_di[-1] if not np.isnan(plus_di[-1]) else 0
        indicators['Minus_DI'] = minus_di[-1] if not np.isnan(minus_di[-1]) else 0
        
        # Aroon
        aroon_down, aroon_up = talib.AROON(high, low)
        indicators['Aroon_Up'] = aroon_up[-1] if not np.isnan(aroon_up[-1]) else 0
        indicators['Aroon_Down'] = aroon_down[-1] if not np.isnan(aroon_down[-1]) else 0
        indicators['Aroon_Oscillator'] = aroon_up[-1] - aroon_down[-1] if not np.isnan(aroon_up[-1]) else 0
        
        return indicators
    
    def _calculate_momentum_indicators(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate momentum oscillators"""
        
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        volume = df['volume'].values
        
        indicators = {}
        
        # RSI (multiple periods)
        for period in [14, 21, 30]:
            rsi = talib.RSI(close, timeperiod=period)
            indicators[f'RSI_{period}'] = rsi[-1] if not np.isnan(rsi[-1]) else 50
        
        # Stochastic
        slowk, slowd = talib.STOCH(high, low, close)
        indicators['Stoch_K'] = slowk[-1] if not np.isnan(slowk[-1]) else 50
        indicators['Stoch_D'] = slowd[-1] if not np.isnan(slowd[-1]) else 50
        
        # Fast Stochastic
        fastk, fastd = talib.STOCHF(high, low, close)
        indicators['Fast_Stoch_K'] = fastk[-1] if not np.isnan(fastk[-1]) else 50
        indicators['Fast_Stoch_D'] = fastd[-1] if not np.isnan(fastd[-1]) else 50
        
        # Williams %R
        willr = talib.WILLR(high, low, close)
        indicators['Williams_R'] = willr[-1] if not np.isnan(willr[-1]) else -50
        
        # CCI (Commodity Channel Index)
        cci = talib.CCI(high, low, close)
        indicators['CCI'] = cci[-1] if not np.isnan(cci[-1]) else 0
        
        # ROC (Rate of Change)
        for period in [10, 20, 30]:
            roc = talib.ROC(close, timeperiod=period)
            indicators[f'ROC_{period}'] = roc[-1] if not np.isnan(roc[-1]) else 0
        
        # Momentum
        for period in [10, 20]:
            mom = talib.MOM(close, timeperiod=period)
            indicators[f'Momentum_{period}'] = mom[-1] if not np.isnan(mom[-1]) else 0
        
        # Money Flow Index
        mfi = talib.MFI(high, low, close, volume)
        indicators['MFI'] = mfi[-1] if not np.isnan(mfi[-1]) else 50
        
        # Ultimate Oscillator
        uo = talib.ULTOSC(high, low, close)
        indicators['Ultimate_Oscillator'] = uo[-1] if not np.isnan(uo[-1]) else 50
        
        return indicators
    
    def _calculate_volatility_indicators(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate volatility indicators"""
        
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        
        indicators = {}
        
        # Bollinger Bands
        for period in [20, 50]:
            bb_upper, bb_middle, bb_lower = talib.BBANDS(close, timeperiod=period)
            
            indicators[f'BB_Upper_{period}'] = bb_upper[-1] if not np.isnan(bb_upper[-1]) else 0
            indicators[f'BB_Middle_{period}'] = bb_middle[-1] if not np.isnan(bb_middle[-1]) else 0
            indicators[f'BB_Lower_{period}'] = bb_lower[-1] if not np.isnan(bb_lower[-1]) else 0
            
            # BB Position (where price is within bands)
            if not np.isnan(bb_upper[-1]) and not np.isnan(bb_lower[-1]):
                bb_position = (close[-1] - bb_lower[-1]) / (bb_upper[-1] - bb_lower[-1])
                indicators[f'BB_Position_{period}'] = bb_position
            
            # BB Width (volatility measure)
            if not np.isnan(bb_upper[-1]) and not np.isnan(bb_lower[-1]):
                bb_width = (bb_upper[-1] - bb_lower[-1]) / bb_middle[-1]
                indicators[f'BB_Width_{period}'] = bb_width
        
        # Average True Range
        for period in [14, 20]:
            atr = talib.ATR(high, low, close, timeperiod=period)
            indicators[f'ATR_{period}'] = atr[-1] if not np.isnan(atr[-1]) else 0
            
            # ATR as percentage of price
            indicators[f'ATR_Percent_{period}'] = (atr[-1] / close[-1]) * 100 if not np.isnan(atr[-1]) else 0
        
        # True Range
        tr = talib.TRANGE(high, low, close)
        indicators['True_Range'] = tr[-1] if not np.isnan(tr[-1]) else 0
        
        # Normalized ATR
        natr = talib.NATR(high, low, close)
        indicators['NATR'] = natr[-1] if not np.isnan(natr[-1]) else 0
        
        return indicators
    
    def _calculate_volume_indicators(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate volume-based indicators"""
        
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        volume = df['volume'].values
        
        indicators = {}
        
        # On Balance Volume
        obv = talib.OBV(close, volume)
        indicators['OBV'] = obv[-1] if not np.isnan(obv[-1]) else 0
        
        # Volume moving averages
        for period in [10, 20, 50]:
            vol_ma = talib.SMA(volume, timeperiod=period)
            indicators[f'Volume_MA_{period}'] = vol_ma[-1] if not np.isnan(vol_ma[-1]) else 0
            
            # Volume ratio
            if not np.isnan(vol_ma[-1]) and vol_ma[-1] > 0:
                indicators[f'Volume_Ratio_{period}'] = volume[-1] / vol_ma[-1]
        
        # Accumulation/Distribution Line
        ad = talib.AD(high, low, close, volume)
        indicators['AD_Line'] = ad[-1] if not np.isnan(ad[-1]) else 0
        
        # Chaikin A/D Oscillator
        adosc = talib.ADOSC(high, low, close, volume)
        indicators['AD_Oscillator'] = adosc[-1] if not np.isnan(adosc[-1]) else 0
        
        # Volume Price Trend
        # Custom calculation
        vpt = np.zeros(len(close))
        for i in range(1, len(close)):
            vpt[i] = vpt[i-1] + volume[i] * ((close[i] - close[i-1]) / close[i-1])
        indicators['VPT'] = vpt[-1]
        
        return indicators
    
    def _calculate_support_resistance(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate support and resistance levels"""
        
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        
        indicators = {}
        
        # Pivot Points (Standard)
        if len(df) >= 3:
            prev_high = high[-2]
            prev_low = low[-2]
            prev_close = close[-2]
            
            pivot = (prev_high + prev_low + prev_close) / 3
            r1 = 2 * pivot - prev_low
            s1 = 2 * pivot - prev_high
            r2 = pivot + (prev_high - prev_low)
            s2 = pivot - (prev_high - prev_low)
            
            indicators['Pivot_Point'] = pivot
            indicators['Resistance_1'] = r1
            indicators['Support_1'] = s1
            indicators['Resistance_2'] = r2
            indicators['Support_2'] = s2
            
            # Distance from pivot levels
            indicators['Distance_from_Pivot'] = (close[-1] - pivot) / pivot
            indicators['Distance_from_R1'] = (close[-1] - r1) / r1
            indicators['Distance_from_S1'] = (close[-1] - s1) / s1
        
        # Fibonacci Retracement Levels
        if len(close) >= 50:
            recent_high = np.max(high[-50:])
            recent_low = np.min(low[-50:])
            
            fib_range = recent_high - recent_low
            
            indicators['Fib_23.6'] = recent_high - 0.236 * fib_range
            indicators['Fib_38.2'] = recent_high - 0.382 * fib_range
            indicators['Fib_50.0'] = recent_high - 0.500 * fib_range
            indicators['Fib_61.8'] = recent_high - 0.618 * fib_range
            indicators['Fib_78.6'] = recent_high - 0.786 * fib_range
        
        return indicators
    
    def _calculate_patterns(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate candlestick patterns and chart patterns"""
        
        open_prices = df['open'].values
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        
        indicators = {}
        
        # Candlestick Patterns (using TA-Lib)
        patterns = {
            'Doji': talib.CDLDOJI,
            'Hammer': talib.CDLHAMMER,
            'Hanging_Man': talib.CDLHANGINGMAN,
            'Shooting_Star': talib.CDLSHOOTINGSTAR,
            'Engulfing_Bullish': talib.CDLENGULFING,
            'Harami': talib.CDLHARAMI,
            'Dark_Cloud': talib.CDLDARKCLOUDCOVER,
            'Piercing': talib.CDLPIERCING,
            'Morning_Star': talib.CDLMORNINGSTAR,
            'Evening_Star': talib.CDLEVENINGSTAR,
            'Three_White_Soldiers': talib.CDL3WHITESOLDIERS,
            'Three_Black_Crows': talib.CDL3BLACKCROWS
        }
        
        for pattern_name, pattern_func in patterns.items():
            try:
                pattern_result = pattern_func(open_prices, high, low, close)
                indicators[f'Pattern_{pattern_name}'] = pattern_result[-1] if not np.isnan(pattern_result[-1]) else 0
            except:
                indicators[f'Pattern_{pattern_name}'] = 0
        
        # Price Action Patterns
        if len(close) >= 20:
            # Higher Highs and Higher Lows (Uptrend)
            recent_highs = high[-10:]
            recent_lows = low[-10:]
            
            higher_highs = sum(1 for i in range(1, len(recent_highs)) if recent_highs[i] > recent_highs[i-1])
            higher_lows = sum(1 for i in range(1, len(recent_lows)) if recent_lows[i] > recent_lows[i-1])
            
            indicators['Higher_Highs_Count'] = higher_highs
            indicators['Higher_Lows_Count'] = higher_lows
            indicators['Uptrend_Strength'] = (higher_highs + higher_lows) / 18  # Normalized
        
        return indicators
    
    def _calculate_statistical_indicators(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate statistical and mathematical indicators"""
        
        close = df['close'].values
        returns = np.diff(close) / close[:-1]
        
        indicators = {}
        
        # Statistical measures
        if len(returns) > 0:
            indicators['Returns_Mean'] = np.mean(returns)
            indicators['Returns_Std'] = np.std(returns)
            indicators['Returns_Skewness'] = stats.skew(returns)
            indicators['Returns_Kurtosis'] = stats.kurtosis(returns)
        
        # Z-Score (price relative to recent mean)
        if len(close) >= 20:
            recent_mean = np.mean(close[-20:])
            recent_std = np.std(close[-20:])
            if recent_std > 0:
                indicators['Z_Score'] = (close[-1] - recent_mean) / recent_std
        
        # Linear Regression
        if len(close) >= 20:
            x = np.arange(len(close[-20:]))
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, close[-20:])
            
            indicators['Linear_Regression_Slope'] = slope
            indicators['Linear_Regression_R2'] = r_value ** 2
            indicators['Linear_Regression_Angle'] = np.degrees(np.arctan(slope))
        
        # Correlation with volume
        if len(close) >= 20 and 'volume' in df.columns:
            volume = df['volume'].values
            if len(volume) >= 20:
                correlation = np.corrcoef(close[-20:], volume[-20:])[0, 1]
                indicators['Price_Volume_Correlation'] = correlation if not np.isnan(correlation) else 0
        
        return indicators
    
    def _calculate_custom_algorithms(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate custom proprietary algorithms"""
        
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        volume = df['volume'].values
        
        indicators = {}
        
        # Custom Momentum Algorithm
        if len(close) >= 10:
            short_momentum = (close[-1] - close[-5]) / close[-5]
            long_momentum = (close[-1] - close[-10]) / close[-10]
            momentum_divergence = short_momentum - long_momentum
            
            indicators['Custom_Momentum_Short'] = short_momentum
            indicators['Custom_Momentum_Long'] = long_momentum
            indicators['Momentum_Divergence'] = momentum_divergence
        
        # Custom Volatility Breakout
        if len(close) >= 20:
            recent_volatility = np.std(close[-10:])
            historical_volatility = np.std(close[-20:])
            volatility_ratio = recent_volatility / historical_volatility if historical_volatility > 0 else 1
            
            indicators['Volatility_Breakout'] = volatility_ratio
        
        # Custom Volume Surge Detection
        if len(volume) >= 20:
            avg_volume = np.mean(volume[-20:])
            volume_surge = volume[-1] / avg_volume if avg_volume > 0 else 1
            
            indicators['Volume_Surge'] = volume_surge
        
        # Custom Price Efficiency Ratio
        if len(close) >= 20:
            direction = abs(close[-1] - close[-20])
            volatility_sum = sum(abs(close[i] - close[i-1]) for i in range(-19, 0))
            efficiency = direction / volatility_sum if volatility_sum > 0 else 0
            
            indicators['Price_Efficiency'] = efficiency
        
        # Custom Market Regime Detection
        if len(close) >= 50:
            # Trending vs Ranging market
            ma_20 = np.mean(close[-20:])
            ma_50 = np.mean(close[-50:])
            
            trend_strength = abs(ma_20 - ma_50) / ma_50 if ma_50 > 0 else 0
            indicators['Market_Regime_Trend'] = trend_strength
            
            # Volatility regime
            short_vol = np.std(close[-10:])
            long_vol = np.std(close[-50:])
            vol_regime = short_vol / long_vol if long_vol > 0 else 1
            indicators['Market_Regime_Volatility'] = vol_regime
        
        return indicators
    
    def get_indicator_signals(self, indicators: Dict[str, float]) -> Dict[str, str]:
        """Convert indicator values to trading signals"""
        
        signals = {}
        
        # RSI signals
        rsi_14 = indicators.get('RSI_14', 50)
        if rsi_14 > 70:
            signals['RSI_Signal'] = 'SELL'
        elif rsi_14 < 30:
            signals['RSI_Signal'] = 'BUY'
        else:
            signals['RSI_Signal'] = 'NEUTRAL'
        
        # MACD signals
        macd = indicators.get('MACD', 0)
        macd_signal = indicators.get('MACD_Signal', 0)
        if macd > macd_signal:
            signals['MACD_Signal'] = 'BUY'
        else:
            signals['MACD_Signal'] = 'SELL'
        
        # Bollinger Bands signals
        bb_position = indicators.get('BB_Position_20', 0.5)
        if bb_position > 0.8:
            signals['BB_Signal'] = 'SELL'
        elif bb_position < 0.2:
            signals['BB_Signal'] = 'BUY'
        else:
            signals['BB_Signal'] = 'NEUTRAL'
        
        # Volume signals
        volume_ratio = indicators.get('Volume_Ratio_20', 1)
        if volume_ratio > 1.5:
            signals['Volume_Signal'] = 'STRONG'
        elif volume_ratio < 0.7:
            signals['Volume_Signal'] = 'WEAK'
        else:
            signals['Volume_Signal'] = 'NORMAL'
        
        return signals

# Test function
def test_indicators():
    """Test the technical indicators"""
    
    # Generate sample data
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    
    # Simulate realistic price data
    price = 100
    prices = [price]
    volumes = []
    
    for _ in range(99):
        change = np.random.normal(0, 0.02)
        price *= (1 + change)
        prices.append(price)
        volumes.append(np.random.randint(1000000, 5000000))
    
    volumes.append(np.random.randint(1000000, 5000000))
    
    df = pd.DataFrame({
        'open': [p * (1 + np.random.normal(0, 0.001)) for p in prices],
        'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
        'close': prices,
        'volume': volumes
    }, index=dates)
    
    # Calculate indicators
    calc = TechnicalIndicators()
    indicators = calc.calculate_all_indicators(df)
    signals = calc.get_indicator_signals(indicators)
    
    print("📊 TECHNICAL INDICATORS TEST RESULTS:")
    print(f"Calculated {len(indicators)} indicators")
    print(f"Generated {len(signals)} signals")
    
    # Show some key indicators
    key_indicators = ['RSI_14', 'MACD', 'BB_Position_20', 'ADX', 'Volume_Ratio_20']
    for indicator in key_indicators:
        if indicator in indicators:
            print(f"{indicator}: {indicators[indicator]:.4f}")

if __name__ == "__main__":
    test_indicators() 