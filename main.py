#!/usr/bin/env python
"""
NORYONAI Main Entry Point

Clean, focused entry point for the real LLM-based trading system.
No BS, just working AI trading agents.
"""

import asyncio
import sys
import os
from pathlib import Path
from typing import Dict, Any
import json

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Import our modules
from config.settings import get_config, get_llm_config
from agents.forex_agent import ForexAgent
from core.llm_apis import LLMOrchestrator
from indicators.technical import TechnicalIndicators

class NoryonAI:
    """Main NORYONAI Trading System"""
    
    def __init__(self):
        """Initialize the NORYONAI system"""
        print("🚀 NORYONAI - Real LLM Trading System")
        print("=" * 50)
        
        # Load configuration
        self.config = get_config()
        self.llm_config = get_llm_config()
        
        # Initialize core components
        self.llm_orchestrator = None
        self.technical_indicators = TechnicalIndicators()
        
        # Initialize agents
        self.forex_agent = None
        self.crypto_agent = None  # TODO: Implement
        self.equity_agent = None  # TODO: Implement
        
        print("✅ NORYONAI system initialized")
    
    async def initialize_agents(self):
        """Initialize trading agents"""
        
        print("🤖 Initializing AI trading agents...")
        
        # Initialize LLM orchestrator
        self.llm_orchestrator = LLMOrchestrator(self.llm_config)
        
        # Initialize Forex agent
        if self.config.trading_config.trade_forex:
            self.forex_agent = ForexAgent(self.llm_config)
            print("✅ Forex agent ready")
        
        # TODO: Initialize other agents
        # if self.config.trading_config.trade_crypto:
        #     self.crypto_agent = CryptoAgent(self.llm_config)
        
        # if self.config.trading_config.trade_equity:
        #     self.equity_agent = EquityAgent(self.llm_config)
        
        print("🎯 All agents initialized and ready for trading")
    
    async def analyze_symbol(self, symbol: str, market_type: str = "forex") -> Dict[str, Any]:
        """Analyze a trading symbol"""
        
        print(f"🔍 Analyzing {symbol} ({market_type})...")
        
        if market_type.lower() == "forex" and self.forex_agent:
            return await self.forex_agent.analyze_forex_pair(symbol)
        else:
            return {"error": f"No agent available for {market_type}"}
    
    async def get_trading_recommendations(self, symbols: list = None) -> Dict[str, Any]:
        """Get trading recommendations for multiple symbols"""
        
        if not symbols:
            # Default symbols to analyze
            symbols = [
                ("EURUSD", "forex"),
                ("GBPUSD", "forex"),
                ("USDJPY", "forex")
            ]
        
        recommendations = {}
        
        for symbol, market_type in symbols:
            try:
                analysis = await self.analyze_symbol(symbol, market_type)
                recommendations[symbol] = analysis
            except Exception as e:
                recommendations[symbol] = {"error": str(e)}
        
        return recommendations
    
    def print_system_status(self):
        """Print system status"""
        
        print("\n📊 NORYONAI SYSTEM STATUS")
        print("=" * 50)
        
        # Configuration status
        self.config.print_status()
        
        # Agent status
        print("🤖 Trading Agents:")
        print(f"  Forex Agent: {'✅ Ready' if self.forex_agent else '❌ Not initialized'}")
        print(f"  Crypto Agent: {'🚧 Coming soon' if self.config.trading_config.trade_crypto else '❌ Disabled'}")
        print(f"  Equity Agent: {'🚧 Coming soon' if self.config.trading_config.trade_equity else '❌ Disabled'}")
        
        # Technical indicators
        print(f"\n📈 Technical Indicators: ✅ Ready ({len(self.technical_indicators.__dict__)} algorithms)")
        
        print()

async def main_menu():
    """Interactive main menu"""
    
    # Initialize system
    noryonai = NoryonAI()
    await noryonai.initialize_agents()
    
    while True:
        print("\n🎯 NORYONAI TRADING SYSTEM")
        print("=" * 30)
        print("1. 📊 System Status")
        print("2. 💱 Analyze Forex Pair")
        print("3. 🎯 Get Trading Recommendations")
        print("4. ⚙️ Configuration")
        print("5. 🧪 Test System")
        print("0. 🚪 Exit")
        print()
        
        choice = input("Select option: ").strip()
        
        if choice == '1':
            noryonai.print_system_status()
            
        elif choice == '2':
            pair = input("Enter forex pair (e.g., EURUSD): ").strip().upper()
            if pair:
                result = await noryonai.analyze_symbol(pair, "forex")
                print("\n💱 FOREX ANALYSIS RESULT:")
                print("=" * 40)
                
                if 'error' in result:
                    print(f"❌ Error: {result['error']}")
                else:
                    # Print key information
                    recommendation = result.get('recommendation', {})
                    print(f"Symbol: {result.get('pair', pair)}")
                    print(f"Action: {recommendation.get('action', 'N/A')}")
                    print(f"Confidence: {recommendation.get('confidence', 0):.1%}")
                    print(f"Position Size: {recommendation.get('position_size', 0):.2%}")
                    
                    if recommendation.get('reasoning'):
                        print(f"\nReasoning:\n{recommendation['reasoning']}")
            
        elif choice == '3':
            print("🎯 Getting trading recommendations...")
            recommendations = await noryonai.get_trading_recommendations()
            
            print("\n📈 TRADING RECOMMENDATIONS:")
            print("=" * 40)
            
            for symbol, analysis in recommendations.items():
                if 'error' in analysis:
                    print(f"{symbol}: ❌ {analysis['error']}")
                else:
                    rec = analysis.get('recommendation', {})
                    action = rec.get('action', 'HOLD')
                    confidence = rec.get('confidence', 0)
                    
                    emoji = "🟢" if action == "BUY" else "🔴" if action == "SELL" else "🟡"
                    print(f"{symbol}: {emoji} {action} (Confidence: {confidence:.1%})")
            
        elif choice == '4':
            print("\n⚙️ CONFIGURATION OPTIONS:")
            print("1. Show current configuration")
            print("2. Create sample configuration")
            print("3. Save current configuration")
            
            config_choice = input("Select option: ").strip()
            
            if config_choice == '1':
                noryonai.config.print_status()
            elif config_choice == '2':
                noryonai.config.create_sample_config()
            elif config_choice == '3':
                noryonai.config.save_config()
            
        elif choice == '5':
            print("🧪 Running system tests...")
            
            # Test technical indicators
            print("Testing technical indicators...")
            from indicators.technical import test_indicators
            test_indicators()
            
            # Test configuration
            print("\nTesting configuration...")
            from config.settings import test_config
            test_config()
            
            print("✅ All tests completed")
            
        elif choice == '0':
            print("👋 Goodbye! Happy trading!")
            break
            
        else:
            print("❌ Invalid option. Please try again.")
        
        input("\nPress Enter to continue...")

async def quick_demo():
    """Quick demo of the system"""
    
    print("🚀 NORYONAI QUICK DEMO")
    print("=" * 30)
    
    # Initialize system
    noryonai = NoryonAI()
    await noryonai.initialize_agents()
    
    # Show status
    noryonai.print_system_status()
    
    # Demo forex analysis
    print("📊 Demo: Analyzing EURUSD...")
    result = await noryonai.analyze_symbol("EURUSD", "forex")
    
    if 'error' not in result:
        rec = result.get('recommendation', {})
        print(f"✅ Analysis complete!")
        print(f"   Action: {rec.get('action', 'N/A')}")
        print(f"   Confidence: {rec.get('confidence', 0):.1%}")
        print(f"   Position Size: {rec.get('position_size', 0):.2%}")
    else:
        print(f"❌ Demo failed: {result['error']}")
    
    print("\n🎯 Demo complete! Use 'python main.py' for full interface.")

def print_help():
    """Print help information"""
    
    print("""
🚀 NORYONAI - Real LLM Trading System

USAGE:
    python main.py              # Interactive menu
    python main.py --demo       # Quick demo
    python main.py --status     # System status
    python main.py --config     # Configuration setup

FEATURES:
    🤖 Multi-LLM Analysis (Claude, OpenAI, Gemini, DeepSeek)
    💱 Forex Trading Agent with 100+ technical indicators
    📊 Real-time market analysis and recommendations
    ⚙️ Configurable risk management and position sizing
    🎯 Production-ready trading signals

SETUP:
    1. Copy config/config_sample.json to config/config.json
    2. Add your API keys (OpenAI, Claude, Gemini, DeepSeek)
    3. Add data provider keys (Alpha Vantage, OANDA, Binance)
    4. Run: python main.py

API KEYS NEEDED:
    - OpenAI API key
    - Claude API key  
    - Gemini API key
    - DeepSeek API key
    - Data provider keys (optional, will use mock data)

For more info: https://github.com/your-repo/noryonai
""")

if __name__ == "__main__":
    
    # Handle command line arguments
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        
        if arg in ['--help', '-h']:
            print_help()
        elif arg == '--demo':
            asyncio.run(quick_demo())
        elif arg == '--status':
            noryonai = NoryonAI()
            noryonai.print_system_status()
        elif arg == '--config':
            from config.settings import test_config
            test_config()
        else:
            print(f"❌ Unknown argument: {arg}")
            print("Use --help for usage information")
    else:
        # Run interactive menu
        try:
            asyncio.run(main_menu())
        except KeyboardInterrupt:
            print("\n👋 Goodbye! Happy trading!")
        except Exception as e:
            print(f"❌ Error: {e}")
            print("Use --help for usage information") 