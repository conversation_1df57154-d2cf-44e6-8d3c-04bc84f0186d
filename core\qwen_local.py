#!/usr/bin/env python
"""
NORYONAI Qwen Local Integration

Local Qwen model for trading analysis - no API costs, full privacy.
"""

import torch
import asyncio
import json
from typing import Dict, Any, Optional
from datetime import datetime
from transformers import AutoTokenizer, AutoModelForCausalLM
import logging

class QwenLocalAnalyzer:
    """Local Qwen model for trading analysis"""
    
    def __init__(self, model_path: str = "Qwen/Qwen2.5-7B-Instruct"):
        """Initialize Qwen model locally"""
        self.model_path = model_path
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.max_length = 2048
        
        print(f"🧠 Initializing Qwen Local Analyzer on {self.device}")
    
    async def initialize_model(self):
        """Load Qwen model asynchronously"""
        
        print("📥 Loading Qwen model (this may take a moment)...")
        
        try:
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path,
                trust_remote_code=True
            )
            
            # Load model with optimizations
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
                device_map="auto" if self.device == "cuda" else None,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            if self.device == "cpu":
                self.model = self.model.to(self.device)
            
            print("✅ Qwen model loaded successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to load Qwen model: {e}")
            print("💡 Try: pip install transformers torch")
            return False
    
    async def get_trading_analysis(self, 
                                 symbol: str,
                                 market_data: Dict[str, Any],
                                 indicators: Dict[str, Any],
                                 market_type: str = "forex") -> Dict[str, Any]:
        """Get trading analysis from local Qwen model"""
        
        if not self.model or not self.tokenizer:
            await self.initialize_model()
        
        if not self.model:
            return {"error": "Qwen model not available"}
        
        print(f"🧠 Getting Qwen local analysis for {symbol}")
        
        # Prepare trading prompt
        prompt = self._create_trading_prompt(symbol, market_data, indicators, market_type)
        
        # Get analysis
        try:
            analysis = await self._generate_analysis(prompt)
            parsed_analysis = self._parse_qwen_response(analysis, symbol)
            
            return {
                "llm_source": "Qwen_Local",
                "symbol": symbol,
                "analysis": parsed_analysis,
                "raw_response": analysis,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Qwen analysis failed: {e}")
            return {"error": str(e)}
    
    def _create_trading_prompt(self, 
                             symbol: str, 
                             market_data: Dict[str, Any],
                             indicators: Dict[str, Any],
                             market_type: str) -> str:
        """Create specialized trading prompt for Qwen"""
        
        prompt = f"""<|im_start|>system
You are a professional quantitative trading analyst specializing in {market_type} markets. Analyze the provided market data and technical indicators to make a trading recommendation.

Provide your analysis in this exact JSON format:
{{
    "action": "BUY|SELL|HOLD",
    "confidence": 0.XX,
    "reasoning": "detailed explanation",
    "risk_level": "LOW|MEDIUM|HIGH",
    "key_factors": ["factor1", "factor2", "factor3"]
}}
<|im_end|>

<|im_start|>user
TRADING ANALYSIS REQUEST - {market_type.upper()} MARKET

Symbol: {symbol}
Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}

MARKET DATA:
- Price: {market_data.get('price', 'N/A')}
- Volume: {market_data.get('volume', 'N/A')}
- 24h Change: {market_data.get('change_24h', 'N/A')}%
- Spread: {market_data.get('spread', 'N/A')}
- Volatility: {market_data.get('volatility', 'N/A')}

TECHNICAL INDICATORS:
"""
        
        # Add key indicators
        key_indicators = [
            'RSI_14', 'MACD', 'MACD_Signal', 'BB_Position_20', 'ADX',
            'Volume_Ratio_20', 'ATR_Percent_14', 'Stoch_K', 'Williams_R'
        ]
        
        for indicator in key_indicators:
            if indicator in indicators:
                prompt += f"- {indicator}: {indicators[indicator]:.4f}\n"
        
        # Add forex-specific indicators if available
        if market_type == "forex":
            forex_indicators = [
                'Active_Sessions_Count', 'Major_Pair_Status', 'Recent_Range_Pips',
                'Carry_Trade_Potential', 'Session_Overlap'
            ]
            for indicator in forex_indicators:
                if indicator in indicators:
                    prompt += f"- {indicator}: {indicators[indicator]}\n"
        
        prompt += f"""
ANALYSIS REQUIREMENTS:
1. Analyze the technical setup comprehensively
2. Consider {market_type} market characteristics
3. Provide clear BUY/SELL/HOLD recommendation
4. Give confidence level (0.0 to 1.0)
5. Assess risk level (LOW/MEDIUM/HIGH)
6. Explain key factors driving your decision
7. Respond ONLY with the requested JSON format

<|im_end|>

<|im_start|>assistant
"""
        
        return prompt
    
    async def _generate_analysis(self, prompt: str) -> str:
        """Generate analysis using Qwen model"""
        
        # Tokenize input
        inputs = self.tokenizer(
            prompt,
            return_tensors="pt",
            truncation=True,
            max_length=self.max_length - 512  # Leave room for response
        ).to(self.device)
        
        # Generate response
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=512,
                temperature=0.1,  # Low temperature for consistent analysis
                do_sample=True,
                top_p=0.9,
                repetition_penalty=1.1,
                pad_token_id=self.tokenizer.eos_token_id
            )
        
        # Decode response
        response = self.tokenizer.decode(
            outputs[0][inputs['input_ids'].shape[1]:],
            skip_special_tokens=True
        )
        
        return response.strip()
    
    def _parse_qwen_response(self, response: str, symbol: str) -> Dict[str, Any]:
        """Parse Qwen response into structured format"""
        
        try:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            
            if json_match:
                json_str = json_match.group(0)
                parsed = json.loads(json_str)
                
                # Validate required fields
                required_fields = ['action', 'confidence', 'reasoning', 'risk_level']
                if all(field in parsed for field in required_fields):
                    return {
                        "action": parsed['action'].upper(),
                        "confidence": float(parsed['confidence']),
                        "reasoning": parsed['reasoning'],
                        "risk_level": parsed['risk_level'].upper(),
                        "key_factors": parsed.get('key_factors', []),
                        "symbol": symbol,
                        "timestamp": datetime.now()
                    }
            
            # Fallback parsing if JSON extraction fails
            return self._fallback_parse(response, symbol)
            
        except Exception as e:
            print(f"⚠️ Qwen response parsing failed: {e}")
            return self._fallback_parse(response, symbol)
    
    def _fallback_parse(self, response: str, symbol: str) -> Dict[str, Any]:
        """Fallback parsing for non-JSON responses"""
        
        text = response.lower()
        
        # Extract action
        if "buy" in text and "sell" not in text:
            action = "BUY"
        elif "sell" in text and "buy" not in text:
            action = "SELL"
        else:
            action = "HOLD"
        
        # Extract confidence
        import re
        conf_match = re.search(r'confidence[:\s]*([0-9.]+)', text)
        confidence = float(conf_match.group(1)) if conf_match else 0.5
        
        # Ensure confidence is in 0-1 range
        if confidence > 1:
            confidence = confidence / 100
        
        # Extract risk level
        if "high risk" in text or "risky" in text:
            risk_level = "HIGH"
        elif "low risk" in text or "safe" in text:
            risk_level = "LOW"
        else:
            risk_level = "MEDIUM"
        
        return {
            "action": action,
            "confidence": confidence,
            "reasoning": response[:300],  # Truncate
            "risk_level": risk_level,
            "key_factors": [],
            "symbol": symbol,
            "timestamp": datetime.now()
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        
        if not self.model:
            return {"status": "not_loaded"}
        
        return {
            "status": "loaded",
            "model_path": self.model_path,
            "device": self.device,
            "model_size": sum(p.numel() for p in self.model.parameters()),
            "memory_usage": torch.cuda.memory_allocated() if self.device == "cuda" else "N/A"
        }

# Integration with existing LLM orchestrator
class QwenIntegratedOrchestrator:
    """Extended LLM orchestrator with Qwen integration"""
    
    def __init__(self, config: Dict[str, str], enable_qwen: bool = True):
        """Initialize with optional Qwen integration"""
        
        # Import existing orchestrator
        from core.llm_apis import LLMOrchestrator
        self.base_orchestrator = LLMOrchestrator(config)
        
        # Initialize Qwen if enabled
        self.qwen_analyzer = None
        if enable_qwen:
            self.qwen_analyzer = QwenLocalAnalyzer()
        
        print(f"🤖 Extended LLM Orchestrator initialized (Qwen: {'✅' if enable_qwen else '❌'})")
    
    async def get_trading_consensus_with_qwen(self,
                                            symbol: str,
                                            market_data: Dict[str, Any],
                                            indicators: Dict[str, Any],
                                            market_type: str = "forex") -> Dict[str, Any]:
        """Get consensus including Qwen local analysis"""
        
        print(f"🧠 Getting extended multi-LLM analysis for {symbol}")
        
        # Get base LLM consensus (Claude, OpenAI, Gemini, DeepSeek)
        base_consensus = await self.base_orchestrator.get_trading_consensus(
            symbol, market_data, indicators, market_type
        )
        
        # Add Qwen analysis if available
        if self.qwen_analyzer:
            try:
                qwen_result = await self.qwen_analyzer.get_trading_analysis(
                    symbol, market_data, indicators, market_type
                )
                
                if "error" not in qwen_result:
                    # Add Qwen to the analysis
                    qwen_analysis = qwen_result["analysis"]
                    base_consensus["individual_analyses"].append({
                        "llm_source": "Qwen_Local",
                        "action": qwen_analysis["action"],
                        "confidence": qwen_analysis["confidence"],
                        "reasoning": qwen_analysis["reasoning"],
                        "risk_level": qwen_analysis["risk_level"]
                    })
                    
                    # Recalculate consensus with Qwen included
                    all_analyses = base_consensus["individual_analyses"]
                    updated_consensus = self._create_extended_consensus(all_analyses)
                    base_consensus["consensus"] = updated_consensus
                    base_consensus["qwen_local"] = qwen_result
                    
                    print("✅ Qwen local analysis included in consensus")
                
            except Exception as e:
                print(f"⚠️ Qwen analysis failed, continuing without: {e}")
        
        return base_consensus
    
    def _create_extended_consensus(self, analyses: list) -> Dict[str, Any]:
        """Create consensus including Qwen analysis"""
        
        if not analyses:
            return {"action": "HOLD", "confidence": 0, "reasoning": "No analyses available"}
        
        # Count votes
        actions = [a["action"] for a in analyses]
        action_counts = {action: actions.count(action) for action in set(actions)}
        
        # Get majority action
        consensus_action = max(action_counts, key=action_counts.get)
        
        # Average confidence for consensus action
        consensus_analyses = [a for a in analyses if a["action"] == consensus_action]
        avg_confidence = sum(a["confidence"] for a in consensus_analyses) / len(consensus_analyses)
        
        # Include Qwen in reasoning
        llm_sources = [a["llm_source"] for a in analyses]
        reasoning_summary = f"Extended consensus from {len(analyses)} LLMs: "
        reasoning_summary += f"{action_counts[consensus_action]}/{len(analyses)} recommend {consensus_action}. "
        
        if "Qwen_Local" in llm_sources:
            reasoning_summary += "Includes local Qwen analysis. "
        
        return {
            "action": consensus_action,
            "confidence": round(avg_confidence, 2),
            "reasoning": reasoning_summary,
            "vote_breakdown": action_counts,
            "participating_llms": llm_sources
        }

# Test function
async def test_qwen_integration():
    """Test Qwen integration"""
    
    print("🧪 Testing Qwen Local Integration...")
    
    # Test Qwen analyzer
    qwen = QwenLocalAnalyzer()
    
    # Mock data
    market_data = {
        'price': 1.0850,
        'volume': 1500000,
        'change_24h': 0.25,
        'spread': 0.0004,
        'volatility': 1.2
    }
    
    indicators = {
        'RSI_14': 65.5,
        'MACD': 0.0012,
        'BB_Position_20': 0.75,
        'ADX': 28.5,
        'Volume_Ratio_20': 1.2
    }
    
    # Test analysis
    result = await qwen.get_trading_analysis("EURUSD", market_data, indicators, "forex")
    
    print("🎯 Qwen Local Analysis Result:")
    print(json.dumps(result, indent=2, default=str))
    
    # Test model info
    info = qwen.get_model_info()
    print(f"\n📊 Model Info: {info}")

if __name__ == "__main__":
    asyncio.run(test_qwen_integration()) 