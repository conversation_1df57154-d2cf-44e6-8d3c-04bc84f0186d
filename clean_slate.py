#!/usr/bin/env python
"""
NORYONAI Clean Slate - Remove All BS, Keep Only Real Stuff

This script will completely clean the codebase and create a focused,
real trading system with Qwen + LLM APIs + Real Tools.
"""

import os
import shutil
from pathlib import Path

def clean_slate():
    """Remove all the BS and create a clean, focused structure"""
    
    print("🧹 CLEANING SLATE - REMOVING ALL BS")
    print("=" * 50)
    
    root = Path(".")
    
    # Directories to completely remove (all the BS)
    remove_dirs = [
        "src", "tests", "scripts", "docs", "reports", "archive",
        "backup_before_cleanup", "__pycache__", "core", "deployment",
        "MagicMock", "test_results", "monitoring", "agents", "training",
        "notifications", "qwen ai", "forex simualted data", "test_torch_clean",
        ".cursor", "user_layer", "test_reports", "test_output", "k8s",
        "integration_layer", "htmlcov", "explainability_examples",
        "execution_layer", "examples", "data_layer", "core_infrastructure",
        "ai_core", "Absolute-Zero-Reasoner-master", ".vscode", ".pytest_cache",
        ".github", "temp_eval", "performance_logs"
    ]
    
    # Files to remove (all the cleanup docs and BS)
    remove_files = [
        "CLEANUP_COMPLETE.md", "CODEBASE_CLEANUP_SUMMARY.md",
        "main.py", "paper_trades.json", "notification_config_template.json"
    ]
    
    print("🗑️ Removing BS directories...")
    for dir_name in remove_dirs:
        dir_path = root / dir_name
        if dir_path.exists():
            try:
                shutil.rmtree(dir_path)
                print(f"   ✅ Removed: {dir_name}/")
            except Exception as e:
                print(f"   ⚠️ Could not remove {dir_name}: {e}")
    
    print("\n🗑️ Removing BS files...")
    for file_name in remove_files:
        file_path = root / file_name
        if file_path.exists():
            try:
                file_path.unlink()
                print(f"   ✅ Removed: {file_name}")
            except Exception as e:
                print(f"   ⚠️ Could not remove {file_name}: {e}")
    
    # Create the REAL structure
    print("\n📁 Creating REAL structure...")
    
    real_structure = {
        "core": "Core LLM components",
        "indicators": "Real trading indicators", 
        "agents": "LLM trading agents",
        "tools": "Real trading tools",
        "config": "Configuration and prompts"
    }
    
    for dir_name, description in real_structure.items():
        dir_path = root / dir_name
        dir_path.mkdir(exist_ok=True)
        
        # Create __init__.py
        init_file = dir_path / "__init__.py"
        init_file.write_text(f'"""NORYONAI {description}"""')
        
        print(f"   ✅ Created: {dir_name}/ - {description}")
    
    print("\n✅ CLEAN SLATE COMPLETE!")
    print("Ready to build something REAL!")

if __name__ == "__main__":
    clean_slate() 