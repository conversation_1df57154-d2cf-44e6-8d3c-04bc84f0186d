# NORYONAI Trading System Requirements

# Core dependencies
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0

# Technical analysis
TA-Lib>=0.4.25

# LLM APIs
openai>=1.0.0
anthropic>=0.7.0
google-generativeai>=0.3.0

# Local LLM support (Qwen)
torch>=2.0.0
transformers>=4.35.0
accelerate>=0.24.0
bitsandbytes>=0.41.0
sentencepiece>=0.1.99
huggingface-hub>=0.19.0

# Training and fine-tuning
peft>=0.7.0
datasets>=2.14.0
trl>=0.7.0

# Data providers
requests>=2.31.0
websocket-client>=1.6.0
python-binance>=1.0.17
oandapyV20>=0.6.3

# Async support
aiohttp>=3.8.0
asyncio-throttle>=1.0.2

# Configuration and utilities
python-dotenv>=1.0.0
pydantic>=2.0.0
click>=8.1.0

# Logging and monitoring
structlog>=23.1.0
prometheus-client>=0.17.0

# Optional: Jupyter for analysis
jupyter>=1.0.0
matplotlib>=3.7.0
plotly>=5.15.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.7.0
flake8>=6.0.0 