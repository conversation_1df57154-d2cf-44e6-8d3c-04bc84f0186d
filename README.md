# 🚀 NORYONAI - Real LLM Trading System

**No BS, just working AI trading agents powered by multiple LLMs and comprehensive technical analysis.**

## 🎯 What This Actually Is

NORYONAI is a **focused, production-ready trading system** that combines:
- **4 LLM APIs** (Claude, OpenAI, Gemini, DeepSeek) for intelligent trading decisions
- **100+ technical indicators** and algorithms to feed the AIs
- **Specialized agents** for Forex, Crypto, and Equity markets
- **Real market data integration** with multiple providers
- **Risk management** and position sizing
- **Clean, modular architecture** - no bloated BS

## ✨ Key Features

### 🤖 Multi-LLM Intelligence
- **Claude**: Best for reasoning and analysis
- **OpenAI GPT-4**: Best for pattern recognition
- **Gemini**: Best for multi-modal analysis
- **DeepSeek**: Best for mathematical reasoning
- **Consensus system** combines all LLM perspectives

### 📊 Comprehensive Technical Analysis
- **Trend Indicators**: SMA, EMA, MACD, ADX, Aroon, Parabolic SAR
- **Momentum Oscillators**: RSI, Stochastic, Williams %R, CCI, ROC, MFI
- **Volatility Indicators**: Bollinger Bands, ATR, True Range
- **Volume Analysis**: OBV, A/D Line, Volume ratios
- **Support/Resistance**: Pivot points, Fibonacci levels
- **Pattern Recognition**: 12+ candlestick patterns
- **Custom Algorithms**: Proprietary indicators for market regime detection

### 💱 Market-Specific Agents
- **Forex Agent**: Specialized for currency pairs with session analysis
- **Crypto Agent**: Coming soon - optimized for cryptocurrency markets
- **Equity Agent**: Coming soon - designed for stock market trading

### ⚙️ Production Features
- **Risk Management**: Configurable position sizing and stop losses
- **Real Data Feeds**: Alpha Vantage, OANDA, Binance integration
- **Configuration System**: JSON-based config with environment variable support
- **Async Architecture**: High-performance concurrent analysis
- **Clean Interface**: Simple CLI and programmatic API

## 🏗️ Architecture

```
noryonai/
├── core/
│   ├── llm_apis.py          # Multi-LLM orchestration
│   └── __init__.py
├── indicators/
│   ├── technical.py         # 100+ technical indicators
│   └── __init__.py
├── agents/
│   ├── forex_agent.py       # Forex trading specialist
│   └── __init__.py
├── tools/                   # Coming soon: broker integration
├── config/
│   ├── settings.py          # Configuration management
│   └── __init__.py
├── main.py                  # Main entry point
├── requirements.txt         # Dependencies
└── README.md               # This file
```

## 🚀 Quick Start

### 1. Clone and Setup
```bash
git clone https://github.com/your-repo/noryonai.git
cd noryonai
pip install -r requirements.txt
```

### 2. Configure API Keys
```bash
# Create configuration
python main.py --config

# Edit config/config.json with your API keys:
{
  "llm": {
    "openai_api_key": "your-openai-key",
    "claude_api_key": "your-claude-key",
    "gemini_api_key": "your-gemini-key",
    "deepseek_api_key": "your-deepseek-key"
  },
  "data": {
    "alpha_vantage_key": "your-alpha-vantage-key",
    "oanda_api_key": "your-oanda-key"
  }
}
```

### 3. Run the System
```bash
# Interactive menu
python main.py

# Quick demo
python main.py --demo

# System status
python main.py --status
```

## 💡 Usage Examples

### Analyze a Forex Pair
```python
from main import NoryonAI
import asyncio

async def analyze_eurusd():
    noryonai = NoryonAI()
    await noryonai.initialize_agents()
    
    result = await noryonai.analyze_symbol("EURUSD", "forex")
    
    recommendation = result['recommendation']
    print(f"Action: {recommendation['action']}")
    print(f"Confidence: {recommendation['confidence']:.1%}")
    print(f"Reasoning: {recommendation['reasoning']}")

asyncio.run(analyze_eurusd())
```

### Get Multiple Recommendations
```python
async def get_recommendations():
    noryonai = NoryonAI()
    await noryonai.initialize_agents()
    
    symbols = [
        ("EURUSD", "forex"),
        ("GBPUSD", "forex"),
        ("USDJPY", "forex")
    ]
    
    recommendations = await noryonai.get_trading_recommendations(symbols)
    
    for symbol, analysis in recommendations.items():
        rec = analysis['recommendation']
        print(f"{symbol}: {rec['action']} (Confidence: {rec['confidence']:.1%})")

asyncio.run(get_recommendations())
```

## 🔧 Configuration

### Environment Variables
```bash
# LLM API Keys
export OPENAI_API_KEY="your-openai-key"
export CLAUDE_API_KEY="your-claude-key"
export GEMINI_API_KEY="your-gemini-key"
export DEEPSEEK_API_KEY="your-deepseek-key"

# Data Provider Keys
export ALPHA_VANTAGE_KEY="your-alpha-vantage-key"
export OANDA_API_KEY="your-oanda-key"
export BINANCE_API_KEY="your-binance-key"
```

### Trading Configuration
```json
{
  "trading": {
    "max_position_size": 0.02,      // 2% max per trade
    "max_daily_risk": 0.05,         // 5% max daily risk
    "base_position_size": 0.01,     // 1% base position
    "default_stop_loss_pips": 20,   // Default stop loss
    "trade_forex": true,            // Enable forex trading
    "trade_crypto": true,           // Enable crypto trading
    "trade_equity": true            // Enable equity trading
  }
}
```

## 📊 Technical Indicators

The system calculates **100+ technical indicators** including:

### Trend Indicators
- Simple Moving Average (SMA) - 5, 10, 20, 50, 100, 200 periods
- Exponential Moving Average (EMA) - 5, 10, 20, 50, 100, 200 periods
- MACD (Moving Average Convergence Divergence)
- ADX (Average Directional Index)
- Parabolic SAR
- Aroon Oscillator

### Momentum Oscillators
- RSI (Relative Strength Index) - 14, 21, 30 periods
- Stochastic Oscillator (Fast and Slow)
- Williams %R
- CCI (Commodity Channel Index)
- ROC (Rate of Change) - 10, 20, 30 periods
- Money Flow Index (MFI)
- Ultimate Oscillator

### Volatility Indicators
- Bollinger Bands - 20, 50 periods
- Average True Range (ATR) - 14, 20 periods
- True Range
- Normalized ATR

### Volume Indicators
- On Balance Volume (OBV)
- Accumulation/Distribution Line
- Chaikin A/D Oscillator
- Volume Price Trend (VPT)
- Volume Moving Averages

### Support/Resistance
- Pivot Points (Standard)
- Fibonacci Retracement Levels
- Dynamic Support/Resistance

### Pattern Recognition
- 12+ Candlestick Patterns (Doji, Hammer, Engulfing, etc.)
- Price Action Patterns
- Higher Highs/Lower Lows Detection

### Custom Algorithms
- Market Regime Detection
- Volatility Breakout Detection
- Volume Surge Analysis
- Price Efficiency Ratio
- Custom Momentum Algorithms

## 🎯 LLM Integration

Each LLM has a specific role:

### Claude (Reasoning)
- Detailed technical analysis
- Risk assessment
- Market context interpretation

### OpenAI GPT-4 (Pattern Recognition)
- Chart pattern identification
- Market momentum analysis
- Historical pattern matching

### Gemini (Multi-modal)
- Comprehensive market analysis
- Cross-asset correlation
- Sentiment integration

### DeepSeek (Mathematical)
- Statistical analysis
- Mathematical model validation
- Quantitative reasoning

### Consensus System
- Combines all LLM perspectives
- Weighted voting based on confidence
- Conflict resolution algorithms

## 🛡️ Risk Management

### Position Sizing
- **Base Size**: 1% of account per trade
- **Confidence Multiplier**: Up to 2x based on AI confidence
- **Volatility Adjustment**: Reduced size in high volatility
- **Maximum Position**: 2% of account (configurable)

### Stop Loss & Take Profit
- **Dynamic Stops**: Based on ATR and market conditions
- **Risk/Reward**: Minimum 1:2 ratio
- **Forex-Specific**: Pip-based calculations
- **Trailing Stops**: Coming soon

### Risk Limits
- **Daily Risk**: Maximum 5% daily drawdown
- **Maximum Drawdown**: 10% account protection
- **Position Limits**: Maximum open positions
- **Correlation Limits**: Prevent over-exposure

## 🔌 Data Providers

### Forex Data
- **OANDA**: Professional forex data and execution
- **Alpha Vantage**: Free tier available
- **FXCM**: Coming soon

### Crypto Data
- **Binance**: Comprehensive crypto data
- **Coinbase**: Coming soon
- **Kraken**: Coming soon

### Equity Data
- **Alpha Vantage**: Stock market data
- **Yahoo Finance**: Coming soon
- **IEX Cloud**: Coming soon

## 🧪 Testing

```bash
# Run all tests
python main.py --test

# Test specific components
python -m indicators.technical
python -m config.settings
python -m agents.forex_agent
```

## 🚧 Roadmap

### Phase 1 (Current)
- ✅ Multi-LLM integration
- ✅ Forex agent with 100+ indicators
- ✅ Configuration system
- ✅ Risk management

### Phase 2 (Next)
- 🚧 Crypto trading agent
- 🚧 Equity trading agent
- 🚧 Real broker integration
- 🚧 Backtesting framework

### Phase 3 (Future)
- 🔮 Portfolio management
- 🔮 Advanced risk models
- 🔮 Machine learning integration
- 🔮 Web interface

## ⚠️ Disclaimer

**This is trading software. Trading involves substantial risk of loss.**

- Use at your own risk
- Start with paper trading
- Never risk more than you can afford to lose
- Past performance doesn't guarantee future results
- This is not financial advice

## 📝 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

- **Issues**: GitHub Issues
- **Discussions**: GitHub Discussions
- **Email**: <EMAIL>

---

**Built with ❤️ for serious traders who want real AI-powered trading tools.**