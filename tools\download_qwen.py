#!/usr/bin/env python
"""
NORYONAI Qwen Model Download Manager

Download and manage Qwen models locally for offline trading analysis.
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Optional
import json
import shutil
from huggingface_hub import snapshot_download, login
from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

class QwenModelManager:
    """Manage local Qwen model downloads and storage"""
    
    def __init__(self, base_path: str = None):
        """Initialize model manager"""
        
        # Set base path for models
        if base_path is None:
            self.base_path = Path(__file__).parent.parent / "models" / "qwen_local"
        else:
            self.base_path = Path(base_path)
        
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        # Available Qwen models for trading
        self.available_models = {
            "qwen2.5-7b-instruct": {
                "model_id": "Qwen/Qwen2.5-7B-Instruct",
                "size_gb": 7.0,
                "description": "Best balance of performance and speed for trading",
                "recommended": True
            },
            "qwen2.5-14b-instruct": {
                "model_id": "Qwen/Qwen2.5-14B-Instruct", 
                "size_gb": 14.0,
                "description": "Higher accuracy, slower inference",
                "recommended": False
            },
            "qwen2.5-3b-instruct": {
                "model_id": "Qwen/Qwen2.5-3B-Instruct",
                "size_gb": 3.0,
                "description": "Fastest inference, lower accuracy",
                "recommended": False
            }
        }
        
        print(f"🗂️ Qwen Model Manager initialized")
        print(f"📁 Local storage: {self.base_path}")
    
    def list_available_models(self):
        """List all available Qwen models"""
        
        print("\n🧠 AVAILABLE QWEN MODELS:")
        print("=" * 50)
        
        for key, model in self.available_models.items():
            status = "✅ RECOMMENDED" if model["recommended"] else "⚪ Available"
            print(f"{key}:")
            print(f"  Status: {status}")
            print(f"  Size: {model['size_gb']:.1f} GB")
            print(f"  Description: {model['description']}")
            print(f"  Model ID: {model['model_id']}")
            
            # Check if already downloaded
            local_path = self.base_path / key
            if local_path.exists():
                print(f"  Local Status: ✅ Downloaded")
            else:
                print(f"  Local Status: ❌ Not downloaded")
            print()
    
    def download_model(self, model_key: str, force_redownload: bool = False) -> bool:
        """Download a specific Qwen model"""
        
        if model_key not in self.available_models:
            print(f"❌ Unknown model: {model_key}")
            self.list_available_models()
            return False
        
        model_info = self.available_models[model_key]
        model_id = model_info["model_id"]
        local_path = self.base_path / model_key
        
        # Check if already exists
        if local_path.exists() and not force_redownload:
            print(f"✅ Model {model_key} already downloaded at {local_path}")
            return True
        
        print(f"📥 Downloading {model_key} ({model_info['size_gb']:.1f} GB)...")
        print(f"🎯 Model ID: {model_id}")
        print(f"📁 Local path: {local_path}")
        
        try:
            # Download model files
            snapshot_download(
                repo_id=model_id,
                local_dir=local_path,
                local_dir_use_symlinks=False,
                resume_download=True
            )
            
            print(f"✅ Successfully downloaded {model_key}")
            
            # Test the model
            if self.test_model(model_key):
                print(f"✅ Model {model_key} tested successfully")
                return True
            else:
                print(f"⚠️ Model {model_key} downloaded but failed testing")
                return False
                
        except Exception as e:
            print(f"❌ Failed to download {model_key}: {e}")
            return False
    
    def test_model(self, model_key: str) -> bool:
        """Test a downloaded model"""
        
        local_path = self.base_path / model_key
        if not local_path.exists():
            print(f"❌ Model {model_key} not found locally")
            return False
        
        print(f"🧪 Testing model {model_key}...")
        
        try:
            # Load tokenizer
            tokenizer = AutoTokenizer.from_pretrained(
                str(local_path),
                trust_remote_code=True
            )
            
            # Load model (CPU only for testing)
            model = AutoModelForCausalLM.from_pretrained(
                str(local_path),
                torch_dtype=torch.float32,
                device_map="cpu",
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            # Simple test
            test_prompt = "What is forex trading?"
            inputs = tokenizer(test_prompt, return_tensors="pt")
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=50,
                    temperature=0.1,
                    do_sample=True
                )
            
            response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            print(f"✅ Test response generated successfully")
            
            # Clean up memory
            del model
            del tokenizer
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
            
            return True
            
        except Exception as e:
            print(f"❌ Model test failed: {e}")
            return False
    
    def get_model_path(self, model_key: str) -> Optional[str]:
        """Get local path for a model"""
        
        local_path = self.base_path / model_key
        if local_path.exists():
            return str(local_path)
        return None
    
    def delete_model(self, model_key: str) -> bool:
        """Delete a local model"""
        
        local_path = self.base_path / model_key
        if not local_path.exists():
            print(f"❌ Model {model_key} not found locally")
            return False
        
        try:
            shutil.rmtree(local_path)
            print(f"✅ Deleted model {model_key}")
            return True
        except Exception as e:
            print(f"❌ Failed to delete {model_key}: {e}")
            return False
    
    def get_storage_info(self) -> Dict:
        """Get storage information"""
        
        total_size = 0
        model_info = {}
        
        for model_key in self.available_models:
            local_path = self.base_path / model_key
            if local_path.exists():
                size = sum(f.stat().st_size for f in local_path.rglob('*') if f.is_file())
                total_size += size
                model_info[model_key] = {
                    "size_bytes": size,
                    "size_gb": size / (1024**3),
                    "path": str(local_path)
                }
        
        return {
            "total_size_gb": total_size / (1024**3),
            "models": model_info,
            "base_path": str(self.base_path)
        }
    
    def print_storage_info(self):
        """Print storage information"""
        
        info = self.get_storage_info()
        
        print("\n💾 LOCAL STORAGE INFO:")
        print("=" * 30)
        print(f"Base Path: {info['base_path']}")
        print(f"Total Size: {info['total_size_gb']:.2f} GB")
        print()
        
        if info['models']:
            print("Downloaded Models:")
            for model_key, model_info in info['models'].items():
                print(f"  {model_key}: {model_info['size_gb']:.2f} GB")
        else:
            print("No models downloaded yet")

def main():
    """Main function for command-line usage"""
    
    manager = QwenModelManager()
    
    if len(sys.argv) < 2:
        print("""
🧠 NORYONAI Qwen Model Manager

USAGE:
    python download_qwen.py list                    # List available models
    python download_qwen.py download <model>        # Download specific model
    python download_qwen.py download-recommended    # Download recommended model
    python download_qwen.py test <model>           # Test downloaded model
    python download_qwen.py info                   # Show storage info
    python download_qwen.py delete <model>         # Delete model

EXAMPLES:
    python download_qwen.py download qwen2.5-7b-instruct
    python download_qwen.py download-recommended
    python download_qwen.py test qwen2.5-7b-instruct
""")
        return
    
    command = sys.argv[1].lower()
    
    if command == "list":
        manager.list_available_models()
        
    elif command == "download" and len(sys.argv) > 2:
        model_key = sys.argv[2]
        force = "--force" in sys.argv
        manager.download_model(model_key, force_redownload=force)
        
    elif command == "download-recommended":
        # Download the recommended model
        recommended = [k for k, v in manager.available_models.items() if v["recommended"]]
        if recommended:
            manager.download_model(recommended[0])
        
    elif command == "test" and len(sys.argv) > 2:
        model_key = sys.argv[2]
        manager.test_model(model_key)
        
    elif command == "info":
        manager.print_storage_info()
        
    elif command == "delete" and len(sys.argv) > 2:
        model_key = sys.argv[2]
        confirm = input(f"Delete model {model_key}? (y/N): ").lower()
        if confirm == 'y':
            manager.delete_model(model_key)
        
    else:
        print("❌ Invalid command. Use without arguments to see help.")

if __name__ == "__main__":
    main() 