#!/usr/bin/env python
"""
NORYONAI Qwen Finance Training System

Fine-tune Qwen models on your extensive financial datasets for 
domain-specific trading analysis and improved performance.
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Any, <PERSON><PERSON>
from datetime import datetime, timedelta
import torch
from torch.utils.data import Dataset, DataLoader
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, 
    TrainingArguments, Trainer,
    DataCollatorForLanguageModeling
)
from datasets import Dataset as HFDataset
import logging

class FinanceDatasetProcessor:
    """Process your financial datasets for Qwen training"""
    
    def __init__(self, data_path: str):
        """Initialize with your data directory"""
        self.data_path = Path(data_path)
        self.processed_data = []
        
        # Your available datasets (from your directory structure)
        self.available_datasets = {
            "forex_data": {
                "path": "forex data folder",
                "description": "HISDATA forex tick data + TrueFX data",
                "priority": "HIGH",
                "format": "tick_data"
            },
            "stocks_1min": {
                "path": "paperswithbacktestStocks-1Min-Price",
                "description": "1-minute stock price data",
                "priority": "HIGH", 
                "format": "ohlcv"
            },
            "financial_news": {
                "path": "paperswithbacktestAll-Daily-News",
                "description": "Daily financial news",
                "priority": "MEDIUM",
                "format": "text"
            },
            "sp500_ratios": {
                "path": "pmoe7SP_500_Stocks_Data-ratios_news_price_10_yrs",
                "description": "S&P 500 ratios and 10-year data",
                "priority": "HIGH",
                "format": "fundamental"
            },
            "finance_instruct": {
                "path": "JosephgflowersFinance-Instruct-500k",
                "description": "500k finance instruction dataset",
                "priority": "VERY_HIGH",
                "format": "instruction"
            },
            "trading_candles": {
                "path": "0xMakatrading-candles-subset-qa-format",
                "description": "Trading candles Q&A format",
                "priority": "VERY_HIGH",
                "format": "qa"
            },
            "institutional_trading": {
                "path": "sovaiinstitutional_trading",
                "description": "Institutional trading data",
                "priority": "HIGH",
                "format": "trading_data"
            }
        }
        
        print(f"📊 Finance Dataset Processor initialized")
        print(f"📁 Data path: {self.data_path}")
    
    def analyze_available_data(self) -> Dict[str, Any]:
        """Analyze your available financial datasets"""
        
        print("🔍 Analyzing available financial datasets...")
        
        analysis = {
            "total_datasets": 0,
            "total_size_gb": 0,
            "dataset_details": {},
            "training_potential": {}
        }
        
        for dataset_key, dataset_info in self.available_datasets.items():
            dataset_path = self.data_path / dataset_info["path"]
            
            if dataset_path.exists():
                # Calculate size
                size_bytes = sum(f.stat().st_size for f in dataset_path.rglob('*') if f.is_file())
                size_gb = size_bytes / (1024**3)
                
                # Count files
                file_count = len(list(dataset_path.rglob('*')))
                
                analysis["dataset_details"][dataset_key] = {
                    "exists": True,
                    "size_gb": size_gb,
                    "file_count": file_count,
                    "priority": dataset_info["priority"],
                    "format": dataset_info["format"],
                    "description": dataset_info["description"]
                }
                
                analysis["total_datasets"] += 1
                analysis["total_size_gb"] += size_gb
                
            else:
                analysis["dataset_details"][dataset_key] = {
                    "exists": False,
                    "priority": dataset_info["priority"]
                }
        
        # Training potential assessment
        high_priority_size = sum(
            details["size_gb"] for details in analysis["dataset_details"].values()
            if details.get("exists") and details["priority"] in ["VERY_HIGH", "HIGH"]
        )
        
        analysis["training_potential"] = {
            "high_priority_data_gb": high_priority_size,
            "estimated_training_samples": int(high_priority_size * 1000),  # Rough estimate
            "recommended_approach": self._get_training_recommendation(high_priority_size)
        }
        
        return analysis
    
    def _get_training_recommendation(self, data_size_gb: float) -> str:
        """Get training recommendation based on data size"""
        
        if data_size_gb > 50:
            return "FULL_FINE_TUNING"  # You have massive data
        elif data_size_gb > 10:
            return "LORA_FINE_TUNING"  # Efficient fine-tuning
        elif data_size_gb > 1:
            return "PROMPT_TUNING"     # Light adaptation
        else:
            return "FEW_SHOT_LEARNING" # Use examples in prompts
    
    def create_training_dataset(self, 
                              dataset_keys: List[str] = None,
                              max_samples: int = 100000,
                              test_split: float = 0.1) -> Tuple[List[Dict], List[Dict]]:
        """Create training dataset from your financial data"""
        
        if dataset_keys is None:
            # Use high-priority datasets by default
            dataset_keys = [
                "finance_instruct", "trading_candles", 
                "institutional_trading", "forex_data"
            ]
        
        print(f"📚 Creating training dataset from: {dataset_keys}")
        
        all_samples = []
        
        for dataset_key in dataset_keys:
            if dataset_key not in self.available_datasets:
                print(f"⚠️ Unknown dataset: {dataset_key}")
                continue
            
            dataset_info = self.available_datasets[dataset_key]
            dataset_path = self.data_path / dataset_info["path"]
            
            if not dataset_path.exists():
                print(f"⚠️ Dataset not found: {dataset_path}")
                continue
            
            print(f"📖 Processing {dataset_key}...")
            
            # Process based on format
            if dataset_info["format"] == "instruction":
                samples = self._process_instruction_dataset(dataset_path)
            elif dataset_info["format"] == "qa":
                samples = self._process_qa_dataset(dataset_path)
            elif dataset_info["format"] == "ohlcv":
                samples = self._process_ohlcv_dataset(dataset_path)
            elif dataset_info["format"] == "text":
                samples = self._process_text_dataset(dataset_path)
            else:
                samples = self._process_generic_dataset(dataset_path)
            
            all_samples.extend(samples)
            print(f"✅ Added {len(samples)} samples from {dataset_key}")
        
        # Shuffle and limit
        np.random.shuffle(all_samples)
        all_samples = all_samples[:max_samples]
        
        # Train/test split
        split_idx = int(len(all_samples) * (1 - test_split))
        train_samples = all_samples[:split_idx]
        test_samples = all_samples[split_idx:]
        
        print(f"📊 Created dataset: {len(train_samples)} train, {len(test_samples)} test")
        
        return train_samples, test_samples
    
    def _process_instruction_dataset(self, path: Path) -> List[Dict]:
        """Process instruction-following datasets"""
        samples = []
        
        try:
            # Look for JSON/JSONL files
            for file_path in path.rglob("*.json*"):
                if file_path.stat().st_size > 100 * 1024 * 1024:  # Skip files > 100MB
                    continue
                
                try:
                    if file_path.suffix == ".jsonl":
                        with open(file_path, 'r', encoding='utf-8') as f:
                            for line in f:
                                data = json.loads(line.strip())
                                sample = self._format_instruction_sample(data)
                                if sample:
                                    samples.append(sample)
                    else:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            if isinstance(data, list):
                                for item in data:
                                    sample = self._format_instruction_sample(item)
                                    if sample:
                                        samples.append(sample)
                except Exception as e:
                    print(f"⚠️ Error processing {file_path}: {e}")
                    continue
        
        except Exception as e:
            print(f"⚠️ Error in instruction processing: {e}")
        
        return samples[:10000]  # Limit per dataset
    
    def _format_instruction_sample(self, data: Dict) -> Optional[Dict]:
        """Format instruction sample for training"""
        
        # Common instruction formats
        if "instruction" in data and "output" in data:
            prompt = f"<|im_start|>system\nYou are a professional financial trading analyst.<|im_end|>\n<|im_start|>user\n{data['instruction']}<|im_end|>\n<|im_start|>assistant\n"
            completion = data["output"]
        elif "question" in data and "answer" in data:
            prompt = f"<|im_start|>system\nYou are a professional financial trading analyst.<|im_end|>\n<|im_start|>user\n{data['question']}<|im_end|>\n<|im_start|>assistant\n"
            completion = data["answer"]
        elif "input" in data and "output" in data:
            prompt = f"<|im_start|>system\nYou are a professional financial trading analyst.<|im_end|>\n<|im_start|>user\n{data['input']}<|im_end|>\n<|im_start|>assistant\n"
            completion = data["output"]
        else:
            return None
        
        return {
            "text": prompt + completion + "<|im_end|>",
            "type": "instruction"
        }
    
    def _process_qa_dataset(self, path: Path) -> List[Dict]:
        """Process Q&A format datasets"""
        samples = []
        
        # Similar to instruction processing but for Q&A format
        # Implementation would be similar to _process_instruction_dataset
        # but adapted for Q&A specific formats
        
        return samples[:5000]
    
    def _process_ohlcv_dataset(self, path: Path) -> List[Dict]:
        """Process OHLCV trading data into training samples"""
        samples = []
        
        try:
            for file_path in path.rglob("*.csv"):
                if file_path.stat().st_size > 50 * 1024 * 1024:  # Skip large files
                    continue
                
                try:
                    df = pd.read_csv(file_path, nrows=1000)  # Limit rows
                    
                    # Create trading analysis samples
                    for i in range(10, len(df), 20):  # Sample every 20 rows
                        window = df.iloc[i-10:i]
                        
                        # Create a trading prompt
                        prompt = self._create_ohlcv_prompt(window)
                        completion = self._create_ohlcv_completion(window)
                        
                        if prompt and completion:
                            samples.append({
                                "text": prompt + completion + "<|im_end|>",
                                "type": "ohlcv_analysis"
                            })
                
                except Exception as e:
                    continue
        
        except Exception as e:
            print(f"⚠️ Error in OHLCV processing: {e}")
        
        return samples[:3000]
    
    def _create_ohlcv_prompt(self, window: pd.DataFrame) -> str:
        """Create trading prompt from OHLCV data"""
        
        if len(window) < 5:
            return None
        
        # Get basic stats
        current_price = window['close'].iloc[-1] if 'close' in window.columns else window.iloc[-1, 1]
        price_change = ((current_price - window.iloc[0, 1]) / window.iloc[0, 1]) * 100
        
        prompt = f"""<|im_start|>system
You are a professional trading analyst. Analyze the provided market data and give a trading recommendation.
<|im_end|>

<|im_start|>user
Analyze this market data and provide a trading recommendation:

Current Price: {current_price:.4f}
Price Change: {price_change:.2f}%
Data Points: {len(window)}

Based on this data, what is your trading recommendation?
<|im_end|>

<|im_start|>assistant
"""
        
        return prompt
    
    def _create_ohlcv_completion(self, window: pd.DataFrame) -> str:
        """Create completion for OHLCV analysis"""
        
        current_price = window.iloc[-1, 1] if len(window.columns) > 1 else window.iloc[-1, 0]
        prev_price = window.iloc[0, 1] if len(window.columns) > 1 else window.iloc[0, 0]
        
        change_pct = ((current_price - prev_price) / prev_price) * 100
        
        if change_pct > 2:
            action = "BUY"
            reasoning = "Strong upward momentum detected"
        elif change_pct < -2:
            action = "SELL"
            reasoning = "Downward trend identified"
        else:
            action = "HOLD"
            reasoning = "Sideways movement, wait for clearer signal"
        
        return f"""Based on the market data analysis:

**Recommendation: {action}**
**Confidence: 0.75**
**Reasoning: {reasoning}**

The price movement of {change_pct:.2f}% indicates {reasoning.lower()}. Risk management is essential."""
    
    def _process_text_dataset(self, path: Path) -> List[Dict]:
        """Process text-based financial datasets"""
        samples = []
        
        # Process financial news, reports, etc.
        # Implementation would extract financial insights from text
        
        return samples[:2000]
    
    def _process_generic_dataset(self, path: Path) -> List[Dict]:
        """Generic processor for unknown formats"""
        samples = []
        
        # Basic processing for any dataset
        # Try to extract meaningful financial information
        
        return samples[:1000]

class QwenFinanceTrainer:
    """Fine-tune Qwen models on financial data"""
    
    def __init__(self, 
                 model_path: str,
                 output_dir: str = "models/qwen_finance_tuned"):
        """Initialize trainer"""
        
        self.model_path = model_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Training configuration
        self.training_config = {
            "learning_rate": 2e-5,
            "batch_size": 4,
            "gradient_accumulation_steps": 4,
            "num_epochs": 3,
            "warmup_steps": 100,
            "save_steps": 500,
            "eval_steps": 500,
            "max_length": 2048
        }
        
        print(f"🎓 Qwen Finance Trainer initialized")
        print(f"📁 Output directory: {self.output_dir}")
    
    def prepare_model_and_tokenizer(self):
        """Load model and tokenizer for training"""
        
        print("📥 Loading model and tokenizer...")
        
        # Load tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.model_path,
            trust_remote_code=True,
            padding_side="right"
        )
        
        # Add pad token if missing
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # Load model
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_path,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True
        )
        
        print("✅ Model and tokenizer loaded")
    
    def create_training_dataset(self, samples: List[Dict]) -> HFDataset:
        """Create HuggingFace dataset from samples"""
        
        print(f"📚 Creating training dataset from {len(samples)} samples...")
        
        def tokenize_function(examples):
            # Tokenize the text
            tokenized = self.tokenizer(
                examples["text"],
                truncation=True,
                padding=False,
                max_length=self.training_config["max_length"],
                return_tensors=None
            )
            
            # For causal LM, labels are the same as input_ids
            tokenized["labels"] = tokenized["input_ids"].copy()
            
            return tokenized
        
        # Convert to HuggingFace dataset
        dataset = HFDataset.from_list(samples)
        
        # Tokenize
        tokenized_dataset = dataset.map(
            tokenize_function,
            batched=True,
            remove_columns=dataset.column_names
        )
        
        print(f"✅ Tokenized dataset created: {len(tokenized_dataset)} samples")
        
        return tokenized_dataset
    
    def train(self, 
              train_dataset: HFDataset,
              eval_dataset: HFDataset = None,
              use_lora: bool = True):
        """Train the model"""
        
        print(f"🎓 Starting training (LoRA: {'✅' if use_lora else '❌'})...")
        
        if use_lora:
            # Use LoRA for efficient fine-tuning
            from peft import LoraConfig, get_peft_model, TaskType
            
            lora_config = LoraConfig(
                task_type=TaskType.CAUSAL_LM,
                inference_mode=False,
                r=16,
                lora_alpha=32,
                lora_dropout=0.1,
                target_modules=["q_proj", "v_proj", "k_proj", "o_proj"]
            )
            
            self.model = get_peft_model(self.model, lora_config)
            print("✅ LoRA configuration applied")
        
        # Training arguments
        training_args = TrainingArguments(
            output_dir=str(self.output_dir),
            overwrite_output_dir=True,
            num_train_epochs=self.training_config["num_epochs"],
            per_device_train_batch_size=self.training_config["batch_size"],
            gradient_accumulation_steps=self.training_config["gradient_accumulation_steps"],
            learning_rate=self.training_config["learning_rate"],
            warmup_steps=self.training_config["warmup_steps"],
            save_steps=self.training_config["save_steps"],
            eval_steps=self.training_config["eval_steps"] if eval_dataset else None,
            evaluation_strategy="steps" if eval_dataset else "no",
            logging_steps=100,
            save_total_limit=3,
            load_best_model_at_end=True if eval_dataset else False,
            metric_for_best_model="eval_loss" if eval_dataset else None,
            greater_is_better=False,
            dataloader_drop_last=True,
            fp16=True,
            report_to=None  # Disable wandb/tensorboard
        )
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False  # Causal LM, not masked LM
        )
        
        # Create trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            data_collator=data_collator,
            tokenizer=self.tokenizer
        )
        
        # Start training
        print("🚀 Training started...")
        trainer.train()
        
        # Save final model
        trainer.save_model()
        self.tokenizer.save_pretrained(str(self.output_dir))
        
        print(f"✅ Training completed! Model saved to {self.output_dir}")
    
    def test_trained_model(self, test_prompt: str = None):
        """Test the trained model"""
        
        if test_prompt is None:
            test_prompt = """<|im_start|>system
You are a professional financial trading analyst.
<|im_end|>

<|im_start|>user
Analyze EURUSD with RSI at 65, MACD bullish crossover, and price above 20-day MA. What's your recommendation?
<|im_end|>

<|im_start|>assistant
"""
        
        print("🧪 Testing trained model...")
        
        inputs = self.tokenizer(test_prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=200,
                temperature=0.1,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )
        
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        print("🎯 Model Response:")
        print(response[len(test_prompt):])

def main():
    """Main training pipeline"""
    
    print("🎓 NORYONAI Qwen Finance Training Pipeline")
    print("=" * 50)
    
    # Initialize components
    data_processor = FinanceDatasetProcessor("data")
    
    # Analyze available data
    analysis = data_processor.analyze_available_data()
    
    print("\n📊 DATA ANALYSIS RESULTS:")
    print(f"Total datasets: {analysis['total_datasets']}")
    print(f"Total size: {analysis['total_size_gb']:.2f} GB")
    print(f"High-priority data: {analysis['training_potential']['high_priority_data_gb']:.2f} GB")
    print(f"Recommended approach: {analysis['training_potential']['recommended_approach']}")
    
    # Create training dataset
    train_samples, test_samples = data_processor.create_training_dataset()
    
    if not train_samples:
        print("❌ No training samples created. Check your data paths.")
        return
    
    # Initialize trainer
    model_path = "models/qwen_local/qwen2.5-7b-instruct"  # Your downloaded model
    trainer = QwenFinanceTrainer(model_path)
    
    # Prepare model
    trainer.prepare_model_and_tokenizer()
    
    # Create datasets
    train_dataset = trainer.create_training_dataset(train_samples)
    test_dataset = trainer.create_training_dataset(test_samples) if test_samples else None
    
    # Train
    trainer.train(train_dataset, test_dataset, use_lora=True)
    
    # Test
    trainer.test_trained_model()
    
    print("\n🎉 Training pipeline completed!")

if __name__ == "__main__":
    main() 