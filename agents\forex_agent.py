#!/usr/bin/env python
"""
NORYONAI Forex Trading Agent

Specialized agent for forex trading using multi-LLM intelligence
and comprehensive technical analysis.
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd
import numpy as np

# Import our core modules
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_apis import LLMOrchestrator
from indicators.technical import TechnicalIndicators

class ForexAgent:
    """Specialized Forex Trading Agent"""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the Forex agent"""
        self.config = config
        self.llm_orchestrator = LLMOrchestrator(config)
        self.technical_indicators = TechnicalIndicators()
        
        # Forex-specific settings
        self.major_pairs = [
            'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 
            'AUDUSD', 'USDCAD', 'NZDUSD'
        ]
        
        self.minor_pairs = [
            'EURJPY', 'GBPJPY', 'EURGBP', 'AUDCAD',
            'AUDCHF', 'AUDJPY', 'CADJPY', 'CHFJPY'
        ]
        
        # Forex market sessions
        self.market_sessions = {
            'Sydney': {'start': 22, 'end': 7},    # UTC hours
            'Tokyo': {'start': 0, 'end': 9},
            'London': {'start': 8, 'end': 17},
            'New_York': {'start': 13, 'end': 22}
        }
        
        print("💱 Forex Trading Agent initialized")
    
    async def analyze_forex_pair(self, 
                                pair: str, 
                                timeframe: str = '1h',
                                lookback_periods: int = 100) -> Dict[str, Any]:
        """Comprehensive analysis of a forex pair"""
        
        print(f"💱 Analyzing {pair} on {timeframe} timeframe")
        
        try:
            # 1. Get market data (you'll need to implement real data feed)
            market_data = await self._get_forex_data(pair, timeframe, lookback_periods)
            
            # 2. Calculate technical indicators
            df = self._prepare_dataframe(market_data)
            indicators = self.technical_indicators.calculate_all_indicators(df)
            
            # 3. Add forex-specific indicators
            forex_indicators = self._calculate_forex_specific_indicators(df, pair)
            indicators.update(forex_indicators)
            
            # 4. Get market context
            market_context = self._get_forex_market_context(pair)
            
            # 5. Get LLM consensus
            llm_analysis = await self.llm_orchestrator.get_trading_consensus(
                pair, market_data, indicators, "forex"
            )
            
            # 6. Generate final recommendation
            recommendation = self._generate_forex_recommendation(
                pair, indicators, llm_analysis, market_context
            )
            
            return {
                'pair': pair,
                'timeframe': timeframe,
                'market_data': market_data,
                'technical_indicators': indicators,
                'market_context': market_context,
                'llm_analysis': llm_analysis,
                'recommendation': recommendation,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Error analyzing {pair}: {e}")
            return {'error': str(e), 'pair': pair}
    
    async def _get_forex_data(self, pair: str, timeframe: str, periods: int) -> Dict[str, Any]:
        """Get real forex market data (implement with your data provider)"""
        
        # This is a mock implementation - replace with real data feed
        # You can use APIs like:
        # - Alpha Vantage
        # - OANDA
        # - FXCM
        # - MetaTrader
        
        print(f"📊 Fetching {pair} data...")
        
        # Mock data for demonstration
        current_price = 1.0850 if 'EUR' in pair else np.random.uniform(0.5, 2.0)
        
        return {
            'symbol': pair,
            'price': current_price,
            'bid': current_price - 0.0002,
            'ask': current_price + 0.0002,
            'spread': 0.0004,
            'volume': np.random.randint(1000000, 10000000),
            'change_24h': np.random.uniform(-1.5, 1.5),
            'high_24h': current_price * (1 + abs(np.random.normal(0, 0.01))),
            'low_24h': current_price * (1 - abs(np.random.normal(0, 0.01))),
            'volatility': np.random.uniform(0.5, 2.0)
        }
    
    def _prepare_dataframe(self, market_data: Dict[str, Any]) -> pd.DataFrame:
        """Prepare DataFrame for technical analysis"""
        
        # Mock historical data - replace with real data
        periods = 100
        base_price = market_data['price']
        
        # Generate realistic forex price movement
        returns = np.random.normal(0, 0.001, periods)  # Small forex movements
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        df = pd.DataFrame({
            'open': [p * (1 + np.random.normal(0, 0.0001)) for p in prices],
            'high': [p * (1 + abs(np.random.normal(0, 0.0005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.0005))) for p in prices],
            'close': prices,
            'volume': [np.random.randint(1000000, 5000000) for _ in range(periods)]
        })
        
        return df
    
    def _calculate_forex_specific_indicators(self, df: pd.DataFrame, pair: str) -> Dict[str, float]:
        """Calculate forex-specific indicators"""
        
        indicators = {}
        close = df['close'].values
        
        # Currency strength indicators
        base_currency = pair[:3]
        quote_currency = pair[3:]
        
        # Carry trade potential (mock calculation)
        indicators['Carry_Trade_Potential'] = np.random.uniform(-2, 5)  # Interest rate differential
        
        # Currency correlation (with major pairs)
        if pair in self.major_pairs:
            indicators['Major_Pair_Status'] = 1
            indicators['Liquidity_Score'] = 10
        else:
            indicators['Major_Pair_Status'] = 0
            indicators['Liquidity_Score'] = np.random.uniform(3, 8)
        
        # Session-based analysis
        current_hour = datetime.now().hour
        active_sessions = self._get_active_sessions(current_hour)
        indicators['Active_Sessions_Count'] = len(active_sessions)
        indicators['Session_Overlap'] = 1 if len(active_sessions) > 1 else 0
        
        # Pip movement analysis
        if len(close) >= 20:
            recent_range = np.max(close[-20:]) - np.min(close[-20:])
            avg_daily_range = np.mean([
                np.max(close[i-20:i]) - np.min(close[i-20:i]) 
                for i in range(20, len(close), 20)
            ]) if len(close) >= 40 else recent_range
            
            indicators['Recent_Range_Pips'] = recent_range * 10000  # Convert to pips
            indicators['Avg_Daily_Range_Pips'] = avg_daily_range * 10000
            indicators['Range_Ratio'] = recent_range / avg_daily_range if avg_daily_range > 0 else 1
        
        # Support/Resistance in pips
        if len(close) >= 50:
            resistance = np.max(close[-50:])
            support = np.min(close[-50:])
            
            indicators['Resistance_Distance_Pips'] = (resistance - close[-1]) * 10000
            indicators['Support_Distance_Pips'] = (close[-1] - support) * 10000
        
        # Trend strength for forex
        if len(close) >= 20:
            sma_20 = np.mean(close[-20:])
            sma_50 = np.mean(close[-50:]) if len(close) >= 50 else sma_20
            
            trend_strength = abs(sma_20 - sma_50) / sma_50 if sma_50 > 0 else 0
            indicators['Forex_Trend_Strength'] = trend_strength * 10000  # In pips
        
        return indicators
    
    def _get_active_sessions(self, current_hour: int) -> List[str]:
        """Get currently active forex sessions"""
        
        active = []
        for session, times in self.market_sessions.items():
            start, end = times['start'], times['end']
            
            if start <= end:  # Same day
                if start <= current_hour <= end:
                    active.append(session)
            else:  # Crosses midnight
                if current_hour >= start or current_hour <= end:
                    active.append(session)
        
        return active
    
    def _get_forex_market_context(self, pair: str) -> Dict[str, Any]:
        """Get forex market context and sentiment"""
        
        base_currency = pair[:3]
        quote_currency = pair[3:]
        
        # Mock market context - replace with real data
        context = {
            'base_currency': base_currency,
            'quote_currency': quote_currency,
            'is_major_pair': pair in self.major_pairs,
            'is_minor_pair': pair in self.minor_pairs,
            'current_session': self._get_active_sessions(datetime.now().hour),
            'market_sentiment': np.random.choice(['RISK_ON', 'RISK_OFF', 'NEUTRAL']),
            'economic_events_today': np.random.randint(0, 5),
            'volatility_environment': np.random.choice(['LOW', 'MEDIUM', 'HIGH']),
            'central_bank_stance': {
                base_currency: np.random.choice(['HAWKISH', 'DOVISH', 'NEUTRAL']),
                quote_currency: np.random.choice(['HAWKISH', 'DOVISH', 'NEUTRAL'])
            }
        }
        
        return context
    
    def _generate_forex_recommendation(self, 
                                     pair: str,
                                     indicators: Dict[str, float],
                                     llm_analysis: Dict[str, Any],
                                     market_context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final forex trading recommendation"""
        
        # Get LLM consensus
        consensus = llm_analysis.get('consensus', {})
        llm_action = consensus.get('action', 'HOLD')
        llm_confidence = consensus.get('confidence', 0.5)
        
        # Technical analysis score
        tech_score = self._calculate_technical_score(indicators)
        
        # Market context score
        context_score = self._calculate_context_score(market_context, pair)
        
        # Combined score
        combined_score = (llm_confidence * 0.4) + (tech_score * 0.4) + (context_score * 0.2)
        
        # Determine final action
        if combined_score > 0.7 and llm_action == 'BUY':
            final_action = 'BUY'
        elif combined_score > 0.7 and llm_action == 'SELL':
            final_action = 'SELL'
        elif combined_score > 0.6:
            final_action = llm_action
        else:
            final_action = 'HOLD'
        
        # Calculate position sizing
        position_size = self._calculate_position_size(combined_score, indicators)
        
        # Set stop loss and take profit
        stop_loss, take_profit = self._calculate_stop_take_profit(
            indicators, final_action, pair
        )
        
        recommendation = {
            'action': final_action,
            'confidence': round(combined_score, 3),
            'position_size': position_size,
            'stop_loss_pips': stop_loss,
            'take_profit_pips': take_profit,
            'risk_reward_ratio': take_profit / stop_loss if stop_loss > 0 else 0,
            'scores': {
                'llm_confidence': llm_confidence,
                'technical_score': tech_score,
                'context_score': context_score,
                'combined_score': combined_score
            },
            'reasoning': self._generate_reasoning(
                llm_analysis, indicators, market_context, final_action
            )
        }
        
        return recommendation
    
    def _calculate_technical_score(self, indicators: Dict[str, float]) -> float:
        """Calculate technical analysis score (0-1)"""
        
        score = 0.5  # Neutral starting point
        
        # RSI contribution
        rsi = indicators.get('RSI_14', 50)
        if rsi > 70:
            score -= 0.2  # Overbought
        elif rsi < 30:
            score += 0.2  # Oversold
        
        # MACD contribution
        macd = indicators.get('MACD', 0)
        macd_signal = indicators.get('MACD_Signal', 0)
        if macd > macd_signal:
            score += 0.1
        else:
            score -= 0.1
        
        # Bollinger Bands contribution
        bb_position = indicators.get('BB_Position_20', 0.5)
        if bb_position > 0.8:
            score -= 0.15  # Near upper band
        elif bb_position < 0.2:
            score += 0.15  # Near lower band
        
        # Trend strength
        adx = indicators.get('ADX', 25)
        if adx > 25:
            score += 0.1  # Strong trend
        
        # Volume confirmation
        volume_ratio = indicators.get('Volume_Ratio_20', 1)
        if volume_ratio > 1.2:
            score += 0.05  # High volume confirmation
        
        return max(0, min(1, score))
    
    def _calculate_context_score(self, context: Dict[str, Any], pair: str) -> float:
        """Calculate market context score (0-1)"""
        
        score = 0.5
        
        # Session activity
        active_sessions = len(context.get('current_session', []))
        if active_sessions >= 2:
            score += 0.2  # High liquidity
        elif active_sessions == 1:
            score += 0.1
        
        # Major pair bonus
        if context.get('is_major_pair', False):
            score += 0.1
        
        # Volatility environment
        volatility = context.get('volatility_environment', 'MEDIUM')
        if volatility == 'MEDIUM':
            score += 0.1  # Optimal for trading
        elif volatility == 'HIGH':
            score -= 0.1  # Higher risk
        
        return max(0, min(1, score))
    
    def _calculate_position_size(self, confidence: float, indicators: Dict[str, float]) -> float:
        """Calculate position size based on confidence and risk"""
        
        base_size = 0.01  # 1% of account
        
        # Adjust based on confidence
        size_multiplier = confidence * 2  # 0-2x multiplier
        
        # Adjust based on volatility
        atr_percent = indicators.get('ATR_Percent_14', 1)
        if atr_percent > 2:
            size_multiplier *= 0.5  # Reduce size in high volatility
        
        return round(base_size * size_multiplier, 3)
    
    def _calculate_stop_take_profit(self, 
                                  indicators: Dict[str, float], 
                                  action: str, 
                                  pair: str) -> tuple:
        """Calculate stop loss and take profit in pips"""
        
        # Base on ATR
        atr_pips = indicators.get('ATR_Percent_14', 1) * 100
        
        # Stop loss: 1.5x ATR
        stop_loss = max(10, round(atr_pips * 1.5))
        
        # Take profit: 2x stop loss (1:2 risk/reward)
        take_profit = stop_loss * 2
        
        # Adjust for major vs minor pairs
        if pair in self.major_pairs:
            stop_loss = max(15, stop_loss)
        else:
            stop_loss = max(25, stop_loss)
            take_profit = stop_loss * 1.5  # Lower R:R for minor pairs
        
        return stop_loss, take_profit
    
    def _generate_reasoning(self, 
                          llm_analysis: Dict[str, Any],
                          indicators: Dict[str, float],
                          context: Dict[str, Any],
                          action: str) -> str:
        """Generate human-readable reasoning"""
        
        reasoning = f"Forex Analysis Summary:\n\n"
        
        # LLM consensus
        consensus = llm_analysis.get('consensus', {})
        reasoning += f"🤖 AI Consensus: {consensus.get('action', 'HOLD')} "
        reasoning += f"(Confidence: {consensus.get('confidence', 0):.1%})\n"
        
        # Technical highlights
        rsi = indicators.get('RSI_14', 50)
        reasoning += f"📊 RSI(14): {rsi:.1f} - "
        if rsi > 70:
            reasoning += "Overbought territory\n"
        elif rsi < 30:
            reasoning += "Oversold territory\n"
        else:
            reasoning += "Neutral zone\n"
        
        # Market context
        sessions = context.get('current_session', [])
        reasoning += f"🌍 Active Sessions: {', '.join(sessions)}\n"
        reasoning += f"💱 Pair Type: {'Major' if context.get('is_major_pair') else 'Minor'}\n"
        
        # Final decision
        reasoning += f"\n🎯 Final Decision: {action}\n"
        
        return reasoning

# Test function
async def test_forex_agent():
    """Test the forex agent"""
    
    # Mock config
    config = {
        'openai_api_key': 'test-key',
        'claude_api_key': 'test-key',
        'gemini_api_key': 'test-key',
        'deepseek_api_key': 'test-key'
    }
    
    agent = ForexAgent(config)
    
    # Test analysis
    result = await agent.analyze_forex_pair('EURUSD', '1h')
    
    print("💱 FOREX AGENT TEST RESULTS:")
    print(json.dumps(result, indent=2, default=str))

if __name__ == "__main__":
    asyncio.run(test_forex_agent()) 